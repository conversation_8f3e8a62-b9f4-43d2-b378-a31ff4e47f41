/* This file is auto-generated by SST. Do not edit. */
/* tslint:disable */
/* eslint-disable */
/* deno-fmt-ignore-file */

declare module "sst" {
  export interface Resource {
    "Api": {
      "type": "sst.cloudflare.Worker"
      "url": string
    }
    "Bucket": {
      "type": "sst.cloudflare.Bucket"
    }
    "GITHUB_APP_ID": {
      "type": "sst.sst.Secret"
      "value": string
    }
    "GITHUB_APP_PRIVATE_KEY": {
      "type": "sst.sst.Secret"
      "value": string
    }
    "Web": {
      "type": "sst.cloudflare.Astro"
      "url": string
    }
  }
}
/// <reference path="sst-env.d.ts" />

import "sst"
export {}