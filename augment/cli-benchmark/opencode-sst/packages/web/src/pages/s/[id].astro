---
import { Base64 } from "js-base64";
import config from "virtual:starlight/user-config";

import config from '../../../config.mjs'
import StarlightPage from '@astrojs/starlight/components/StarlightPage.astro';
import Share from "../../components/Share.tsx";

const apiUrl = import.meta.env.VITE_API_URL;

const { id } = Astro.params;

// For local development demo, use mock data instead of fetching from API
let data;
if (!apiUrl || apiUrl === 'undefined') {
  // Mock data for demonstration
  data = {
    info: {
      id: id,
      title: "Demo Session: Hello World",
      time: {
        created: Date.now() - 3600000, // 1 hour ago
        updated: Date.now() - 1800000  // 30 minutes ago
      },
      version: "0.0.3"
    },
    messages: [
      {
        info: {
          id: "msg_demo_1",
          sessionID: id,
          role: "user",
          time: { created: Date.now() - 3600000 }
        },
        parts: [
          {
            id: "prt_demo_1",
            sessionID: id,
            messageID: "msg_demo_1",
            type: "text",
            text: "Hello, this is a test session for demonstration"
          }
        ]
      },
      {
        info: {
          id: "msg_demo_2",
          sessionID: id,
          role: "assistant",
          time: {
            created: Date.now() - 3500000,
            completed: Date.now() - 3400000
          },
          modelID: "claude-3-5-sonnet-20241022",
          providerID: "anthropic",
          mode: "general",
          system: ["You are a helpful AI assistant."],
          path: { cwd: "/demo", root: "/demo" },
          cost: 0.001,
          tokens: { input: 15, output: 25, reasoning: 0, cache: { read: 0, write: 0 } }
        },
        parts: [
          {
            id: "prt_demo_2",
            sessionID: id,
            messageID: "msg_demo_2",
            type: "text",
            text: "Hello! This is a demonstration of the opencode session sharing interface. This shows how shared sessions would appear when viewed through the web interface.\n\nKey features demonstrated:\n- Session metadata display\n- Message conversation flow\n- User and assistant message formatting\n- Timestamp information\n- Model and provider details"
          }
        ]
      }
    ]
  };
} else {
  try {
    const res = await fetch(`${apiUrl}/share_data?id=${id}`);
    data = await res.json();
  } catch (error) {
    console.error('Failed to fetch session data:', error);
    data = null;
  }
}

if (!data || !data.info) {
  return new Response(null, {
    status: 404,
    statusText: 'Not found'
  });
}

const models: Set<string> = new Set();
const version = data.info.version ? `v${data.info.version}` : "v0.0.1";

Object.values(data.messages).forEach((d) => {
  if (d.role === "assistant" && d.modelID) {
    models.add(d.modelID);
  }
});

const encodedTitle = encodeURIComponent(
  Base64.encode(
    // Convert to ASCII
    encodeURIComponent(
      // Truncate to fit S3's max key size
      data.info.title.substring(0, 700),
    )
  )
);

const modelsArray = Array.from(models);
let modelParam;
if (modelsArray.length === 1) {
  modelParam = modelsArray[0];
}
else if (modelsArray.length === 2) {
  modelParam = encodeURIComponent(`${modelsArray[0]} & ${modelsArray[1]}`);
}
else {
  modelParam = encodeURIComponent(`${modelsArray[0]} & ${modelsArray.length - 1} others`);
}

const ogImage = `${config.socialCard}/opencode-share/${encodedTitle}.png?model=${modelParam}&version=${version}&id=${id}`;
---
<StarlightPage
  hasSidebar={false}
  frontmatter={{
    title: data.info.title,
    pagefind: false,
    template: "splash",
    tableOfContents: false,
    head: [
      {
        tag: "meta",
        attrs: {
          name: "description",
          content: "opencode - The AI coding agent built for the terminal.",
        },
      },
      {
        tag: "meta",
        attrs: {
          property: "og:image",
          content: ogImage,
        },
      },
      {
        tag: "meta",
        attrs: {
          name: "twitter:image",
          content: ogImage,
        },
      },
    ],
  }}
>
  <Share
    id={id}
    api={apiUrl}
    info={data.info}
    messages={data.messages}
    client:only="solid"
  />
</StarlightPage>

<style is:global>
body > .page > .main-frame .main-pane > main > .content-panel:first-of-type {
  display: none;
}
body > .page > .main-frame .main-pane > main {
  padding: 0;
}
body > .page > .main-frame .main-pane > main > .content-panel + .content-panel {
  border-top: none !important;
  padding: 0;
}
</style>
