---
title: Providers
description: Using any LLM provider in opencode.
---

opencode uses the [AI SDK](https://ai-sdk.dev/) and [Models.dev](https://models.dev) to support for **75+ LLM providers** and it supports running local models.

To add a provider you need to:

1. Add the API keys for the provider using `opencode auth login`.
2. Configure the provider in your opencode config.

---

### Credentials

When you add a provider's API keys with `opencode auth login`, they are stored
in `~/.local/share/opencode/auth.json`.

---

### Config

You can customize the providers through the `provider` section in your opencode
config.

---

#### Base URL

You can customize the base URL for any provider by setting the `baseURL` option. This is useful when using proxy services or custom endpoints.

```json title="opencode.json" {6}
{
  "$schema": "https://opencode.ai/config.json",
  "provider": {
    "anthropic": {
      "options": {
        "baseURL": "https://api.anthropic.com/v1"
      }
    }
  }
}
```

---

## Directory

Let's look at some of the providers in detail. If you'd like to add a provider to the
list, feel free to open a PR.

:::note
Don't see a provider here? Submit a PR.
:::

---

### Amazon Bedrock

To use Amazon Bedrock with opencode:

1. Head over to the **Model catalog** in the Amazon Bedrock console and request
   access to the models you want.

   :::tip
   You need to have access to the model you want in Amazon Bedrock.
   :::

1. You'll need either to set one of the following environment variables:

   - `AWS_ACCESS_KEY_ID`: You can get this by creating an IAM user and generating
     an access key for it.
   - `AWS_PROFILE`: First login through AWS IAM Identity Center (or AWS SSO) using
     `aws sso login`. Then get the name of the profile you want to use.
   - `AWS_BEARER_TOKEN_BEDROCK`: You can generate a long-term API key from the
     Amazon Bedrock console.

   Once you have one of the above, set it while running opencode.

   ```bash
   AWS_ACCESS_KEY_ID=XXX opencode
   ```

   Or add it to a `.env` file in the project root.

   ```bash title=".env"
   AWS_ACCESS_KEY_ID=XXX
   ```

   Or add it to your bash profile.

   ```bash title="~/.bash_profile"
   export AWS_ACCESS_KEY_ID=XXX
   ```

1. Run the `/models` command to select the model you want.

---

### Anthropic

We recommend signing up for [Claude Pro](https://www.anthropic.com/news/claude-pro) or [Max](https://www.anthropic.com/max), it's the most cost-effective way to use opencode.

Once you've singed up, run `opencode auth login` and select Anthropic.

```bash
$ opencode auth login

┌  Add credential
│
◆  Select provider
│  ● Anthropic (recommended)
│  ○ OpenAI
│  ○ Google
│  ...
└
```

This will ask you login with your Anthropic account in your browser. Now all the
the Anthropic models should be available when you use the `/models` command.

---

### Azure OpenAI

1. Head over to the [Azure portal](https://portal.azure.com/) and create an **Azure OpenAI** resource. You'll need:

   - **Resource name**: This becomes part of your API endpoint (`https://RESOURCE_NAME.openai.azure.com/`)
   - **API key**: Either `KEY 1` or `KEY 2` from your resource

2. Go to [Azure AI Foundry](https://ai.azure.com/) and deploy a model.

   :::note
   The deployment name must match the model name for opencode to work properly.
   :::

3. Run `opencode auth login` and select **Azure**.

   ```bash
   $ opencode auth login

   ┌  Add credential
   │
   ◆  Select provider
   │  ● Azure
   │  ...
   └
   ```

4. Enter your API key.

   ```bash
   $ opencode auth login

   ┌  Add credential
   │
   ◇  Select provider
   │  Azure
   │
   ◇  Enter your API key
   │  _
   └
   ```

5. Set your resource name as an environment variable:

   ```bash
   AZURE_RESOURCE_NAME=XXX opencode
   ```

   Or add it to a `.env` file in the project root:

   ```bash title=".env"
   AZURE_RESOURCE_NAME=XXX
   ```

   Or add it to your bash profile:

   ```bash title="~/.bash_profile"
   export AZURE_RESOURCE_NAME=XXX
   ```

6. Run the `/models` command to select your deployed model.

---

### GitHub Copilot

To use your GitHub Copilot subscription with opencode:

:::note
Some models might need a [Pro+
subscription](https://github.com/features/copilot/plans) to use.
:::

1. Run `opencode auth login` and select GitHub Copilot.

   ```bash
   $ opencode auth login
   ┌  Add credential

   │
   ◇  Select provider
   │  GitHub Copilot
   │
   ◇   ──────────────────────────────────────────────╮
   │                                                 │
   │  Please visit: https://github.com/login/device  │
   │  Enter code: 8F43-6FCF                          │
   │                                                 │
   ├─────────────────────────────────────────────────╯
   │
   ◓  Waiting for authorization...
   ```

2. Navigate to [github.com/login/device](https://github.com/login/device) and enter the code.

3. Now run the `/models` command to select the model you want.

---

### Groq

1. Head over to the [Groq console](https://console.groq.com/), click **Create API Key**, and copy the key.

2. Run `opencode auth login` and select Groq.

   ```bash
   $ opencode auth login

   ┌  Add credential
   │
   ◆  Select provider
   │  ● Groq
   │  ...
   └
   ```

3. Enter the API key for the provider.

   ```bash
   $ opencode auth login

   ┌  Add credential
   │
   ◇  Select provider
   │  Groq
   │
   ◇  Enter your API key
   │  _
   └
   ```

4. Run the `/models` command to select the one you want.

---

### LM Studio

You can configure opencode to use local models through LM Studio.

```json title="opencode.json" "lmstudio" {5, 6, 8, 10-14}
{
  "$schema": "https://opencode.ai/config.json",
  "provider": {
    "lmstudio": {
      "npm": "@ai-sdk/openai-compatible",
      "name": "LM Studio (local)",
      "options": {
        "baseURL": "http://127.0.0.1:1234/v1"
      },
      "models": {
        "google/gemma-3n-e4b": {
          "name": "Gemma 3n-e4b (local)"
        }
      }
    }
  }
}
```

In this example:

- `lmstudio` is the custom provider ID. This can be any string you want.
- `npm` specifies the package to use for this provider. Here, `@ai-sdk/openai-compatible` is used for any OpenAI-compatible API.
- `name` is the display name for the provider in the UI.
- `options.baseURL` is the endpoint for the local server.
- `models` is a map of model IDs to their configurations. The model name will be displayed in the model selection list.

---

### Ollama

You can configure opencode to use local models through Ollama.

```json title="opencode.json" "ollama" {5, 6, 8, 10-14}
{
  "$schema": "https://opencode.ai/config.json",
  "provider": {
    "ollama": {
      "npm": "@ai-sdk/openai-compatible",
      "name": "Ollama (local)",
      "options": {
        "baseURL": "http://localhost:11434/v1"
      },
      "models": {
        "llama2": {
          "name": "Llama 2"
        }
      }
    }
  }
}
```

In this example:

- `ollama` is the custom provider ID. This can be any string you want.
- `npm` specifies the package to use for this provider. Here, `@ai-sdk/openai-compatible` is used for any OpenAI-compatible API.
- `name` is the display name for the provider in the UI.
- `options.baseURL` is the endpoint for the local server.
- `models` is a map of model IDs to their configurations. The model name will be displayed in the model selection list.

---

### OpenAI

https://platform.openai.com/api-keys

1. Head over to the [OpenAI Platform console](https://platform.openai.com/api-keys), click **Create new secret key**, and copy the key.

2. Run `opencode auth login` and select OpenAI.

   ```bash
   $ opencode auth login

   ┌  Add credential
   │
   ◆  Select provider
   │  ● OpenAI
   │  ...
   └
   ```

3. Enter the API key for the provider.

   ```bash
   $ opencode auth login

   ┌  Add credential
   │
   ◇  Select provider
   │  OpenAI
   │
   ◇  Enter your API key
   │  _
   └
   ```

4. Run the `/models` command to select the one you want.

---

### OpenRouter

1. Head over to the [OpenRouter dashboard](https://openrouter.ai/settings/keys), click **Create API Key**, and copy the key.

2. Run `opencode auth login` and select OpenRouter.

   ```bash
   $ opencode auth login

   ┌  Add credential
   │
   ◆  Select provider
   │  ● OpenRouter
   │  ○ Anthropic
   │  ○ Google
   │  ...
   └
   ```

3. Enter the API key for the provider.

   ```bash
   $ opencode auth login

   ┌  Add credential
   │
   ◇  Select provider
   │  OpenRouter
   │
   ◇  Enter your API key
   │  _
   └
   ```

4. Many OpenRouter models are preloaded by default, run the `/models` command to select the one you want.

   You can also add additional models through your opencode config.

   ```json title="opencode.json" {6}
   {
     "$schema": "https://opencode.ai/config.json",
     "provider": {
       "openrouter": {
         "models": {
           "somecoolnewmodel": {}
         }
       }
     }
   }
   ```

5. You can also customize them through your opencode config. Here's an example of specifying a provider

   ```json title="opencode.json"
   {
     "$schema": "https://opencode.ai/config.json",
     "provider": {
       "openrouter": {
         "models": {
           "moonshotai/kimi-k2": {
             "options": {
               "provider": {
                 "order": ["baseten"],
                 "allow_fallbacks": false
               }
             }
           }
         }
       }
     }
   }
   ```

---

### Cerebras

Cerebras offers fast inference with generous free tiers and competitive pricing.

1. Head over to the [Cerebras console](https://inference.cerebras.ai/), create an account, and generate an API key.

2. Run `opencode auth login` and select **Other**.

   ```bash
   $ opencode auth login

   ┌  Add credential
   │
   ◆  Select provider
   │  ...
   │  ● Other
   └
   ```

3. Enter `cerebras` as the provider ID.

   ```bash
   $ opencode auth login

   ┌  Add credential
   │
   ◇  Select provider
   │  Other
   │
   ◇  Enter provider id
   │  cerebras
   └
   ```

4. Enter your Cerebras API key.

   ```bash
   $ opencode auth login

   ┌  Add credential
   │
   ◇  Enter your API key
   │  csk-...
   └
   ```

5. Configure Cerebras in your opencode config.

   ```json title="opencode.json" "cerebras" {5-19}
   {
     "$schema": "https://opencode.ai/config.json",
     "provider": {
       "cerebras": {
         "npm": "@ai-sdk/cerebras",
         "name": "Cerebras",
         "options": {
           "baseURL": "https://api.cerebras.ai/v1"
         },
         "models": {
           "qwen-3-235b-a22b": {
             "name": "Qwen-3-235b-a22b"
           },
           "llama-3.3-70b": {
             "name": "Llama-3.3-70b"
           }
         }
       }
     }
   }
   ```

6. Run the `/models` command to select a Cerebras model.

---

### DeepSeek

DeepSeek offers powerful reasoning models at competitive prices.

1. Head over to the [DeepSeek console](https://platform.deepseek.com/), create an account, and generate an API key.

2. Run `opencode auth login` and select **Other**.

   ```bash
   $ opencode auth login

   ┌  Add credential
   │
   ◆  Select provider
   │  ...
   │  ● Other
   └
   ```

3. Enter `deepseek` as the provider ID.

   ```bash
   $ opencode auth login

   ┌  Add credential
   │
   ◇  Select provider
   │  Other
   │
   ◇  Enter provider id
   │  deepseek
   └
   ```

4. Enter your DeepSeek API key.

   ```bash
   $ opencode auth login

   ┌  Add credential
   │
   ◇  Enter your API key
   │  sk-...
   └
   ```

5. Configure DeepSeek in your opencode config.

   ```json title="opencode.json" "deepseek" {5-17}
   {
     "$schema": "https://opencode.ai/config.json",
     "provider": {
       "deepseek": {
         "npm": "@ai-sdk/openai-compatible",
         "name": "DeepSeek",
         "options": {
           "baseURL": "https://api.deepseek.com/v1"
         },
         "models": {
           "deepseek-reasoner": {
             "name": "DeepSeek Reasoner"
           }
         }
       }
     }
   }
   ```

6. Run the `/models` command to select a DeepSeek model.

---

### Moonshot AI (Kimi)

Moonshot AI offers the Kimi models with long context capabilities.

1. Head over to the [Moonshot AI console](https://platform.moonshot.cn/), create an account, and generate an API key.

2. Run `opencode auth login` and select **Other**.

   ```bash
   $ opencode auth login

   ┌  Add credential
   │
   ◆  Select provider
   │  ...
   │  ● Other
   └
   ```

3. Enter `moonshot` as the provider ID.

   ```bash
   $ opencode auth login

   ┌  Add credential
   │
   ◇  Select provider
   │  Other
   │
   ◇  Enter provider id
   │  moonshot
   └
   ```

4. Enter your Moonshot API key.

   ```bash
   $ opencode auth login

   ┌  Add credential
   │
   ◇  Enter your API key
   │  sk-...
   └
   ```

5. Configure Moonshot in your opencode config.

   ```json title="opencode.json" "moonshot" {5-17}
   {
     "$schema": "https://opencode.ai/config.json",
     "provider": {
       "moonshot": {
         "npm": "@ai-sdk/openai-compatible",
         "name": "Moonshot AI",
         "options": {
           "baseURL": "https://api.moonshot.cn/v1"
         },
         "models": {
           "kimi-k2-0711-preview": {
             "name": "Kimi K2"
           }
         }
       }
     }
   }
   ```

6. Run the `/models` command to select a Kimi model.

---

### Custom

To add any **OpenAI-compatible** provider that's not listed in `opencode auth login`:

:::tip
You can use any OpenAI-compatible provider with opencode. Most modern AI providers offer OpenAI-compatible APIs.
:::

1. Run `opencode auth login` and scroll down to **Other**.

   ```bash
   $ opencode auth login

   ┌  Add credential
   │
   ◆  Select provider
   │  ...
   │  ● Other
   └
   ```

2. Enter a unique ID for the provider.

   ```bash
   $ opencode auth login

   ┌  Add credential
   │
   ◇  Enter provider id
   │  myprovider
   └
   ```

   :::note
   Choose a memorable ID, you'll use this in your config file.
   :::

3. Enter your API key for the provider.

   ```bash
   $ opencode auth login

   ┌  Add credential
   │
   ▲  This only stores a credential for myprovider - you will need configure it in opencode.json, check the docs for examples.
   │
   ◇  Enter your API key
   │  sk-...
   └
   ```

4. Create or update your `opencode.json` file in your project directory:

   ```json title="opencode.json" ""myprovider"" {5-15}
   {
     "$schema": "https://opencode.ai/config.json",
     "provider": {
       "myprovider": {
         "npm": "@ai-sdk/openai-compatible",
         "name": "My AI ProviderDisplay Name",
         "options": {
           "baseURL": "https://api.myprovider.com/v1"
         },
         "models": {
           "my-model-name": {
             "name": "My Model Display Name"
           }
         }
       }
     }
   }
   ```

   Here are the configuration options:

   - **npm**: AI SDK package to use, `@ai-sdk/openai-compatible` for OpenAI-compatible providers
   - **name**: Display name in UI.
   - **models**: Available models.
   - **options.baseURL**: API endpoint URL.
   - **options.apiKey**: Optionally set the API key, if not using auth.
   - **options.headers**: Optionally set custom headers.

   More on the advanced options in the example below.

5. Run the `/models` command and your custom provider and models will appear in the selection list.

---

##### Example

Here's an example setting the `apiKey` and `headers` options.

```json title="opencode.json" {9,11}
{
  "$schema": "https://opencode.ai/config.json",
  "provider": {
    "myprovider": {
      "npm": "@ai-sdk/openai-compatible",
      "name": "My AI ProviderDisplay Name",
      "options": {
        "baseURL": "https://api.myprovider.com/v1",
        "apiKey": "{env:ANTHROPIC_API_KEY}",
        "headers": {
          "Authorization": "Bearer custom-token"
        }
      },
      "models": {
        "my-model-name": {
          "name": "My Model Display Name"
        }
      }
    }
  }
}
```

We are setting the `apiKey` using the `env` variable syntax, [learn more](/docs/config#env-vars).

#### Common Examples

**Together AI:**

```json title="opencode.json"
{
  "$schema": "https://opencode.ai/config.json",
  "provider": {
    "together": {
      "npm": "@ai-sdk/openai-compatible",
      "name": "Together AI",
      "options": {
        "baseURL": "https://api.together.xyz/v1"
      },
      "models": {
        "meta-llama/Llama-3.2-11B-Vision-Instruct-Turbo": {
          "name": "Llama 3.2 11B Vision"
        }
      }
    }
  }
}
```

**Fireworks AI:**

```json title="opencode.json"
{
  "$schema": "https://opencode.ai/config.json",
  "provider": {
    "fireworks": {
      "npm": "@ai-sdk/openai-compatible",
      "name": "Fireworks AI",
      "options": {
        "baseURL": "https://api.fireworks.ai/inference/v1"
      },
      "models": {
        "accounts/fireworks/models/llama-v3p1-70b-instruct": {
          "name": "Llama 3.1 70B"
        }
      }
    }
  }
}
```

---

## Troubleshooting

If you are having trouble with configuring a provider, check the following:

1. **Check the auth setup**: Run `opencode auth list` to see if the credentials
   for the provider are added to your config.

   This doesn't apply to providers like Amazon Bedrock, that rely on environment variables for their auth.

2. For custom providers, check the opencode config and:

   - Make sure the provider ID used in `opencode auth login` matches the ID in your opencode config.
   - The right npm package is used for the provider. For example, use `@ai-sdk/cerebras` for Cerebras. And for all other OpenAI-compatible providers, use `@ai-sdk/openai-compatible`.
   - Check correct API endpoint is used in the `options.baseURL` field.
