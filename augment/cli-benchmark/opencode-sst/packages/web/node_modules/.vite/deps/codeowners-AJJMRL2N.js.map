{"version": 3, "sources": ["../../../../../node_modules/@shikijs/langs/dist/codeowners.mjs"], "sourcesContent": ["const lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"CODEOWNERS\\\",\\\"name\\\":\\\"codeowners\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#pattern\\\"},{\\\"include\\\":\\\"#owner\\\"}],\\\"repository\\\":{\\\"comment\\\":{\\\"patterns\\\":[{\\\"begin\\\":\\\"^\\\\\\\\s*#\\\",\\\"captures\\\":{\\\"0\\\":{\\\"name\\\":\\\"punctuation.definition.comment.codeowners\\\"}},\\\"end\\\":\\\"$\\\",\\\"name\\\":\\\"comment.line.codeowners\\\"}]},\\\"owner\\\":{\\\"match\\\":\\\"\\\\\\\\S*@\\\\\\\\S+\\\",\\\"name\\\":\\\"storage.type.function.codeowners\\\"},\\\"pattern\\\":{\\\"match\\\":\\\"^\\\\\\\\s*(\\\\\\\\S+)\\\",\\\"name\\\":\\\"variable.other.codeowners\\\"}},\\\"scopeName\\\":\\\"text.codeowners\\\"}\"))\n\nexport default [\nlang\n]\n"], "mappings": ";;;AAAA,IAAM,OAAO,OAAO,OAAO,KAAK,MAAM,meAA2iB,CAAC;AAEllB,IAAO,qBAAQ;AAAA,EACf;AACA;", "names": []}