import "./chunk-5AQFBOJN.js";

// ../../node_modules/marked-shiki/dist/index.js
function o(s = {}) {
  const { highlight: e, container: n } = s;
  return {
    async: true,
    async walkTokens(t) {
      if (t.type !== "code" || typeof e != "function") return;
      const [a = "text", ...i] = t.lang.split(" "), { text: c } = t, r = await e(c, a, i), l = n ? n.replace("%l", String(a).toUpperCase()).replace("%s", r).replace("%t", c) : r;
      Object.assign(t, {
        type: "html",
        block: true,
        text: `${l}
`
      });
    }
  };
}
export {
  o as default
};
//# sourceMappingURL=marked-shiki.js.map
