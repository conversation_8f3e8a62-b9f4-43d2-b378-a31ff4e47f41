{"version": 3, "sources": ["../../../../../node_modules/lang-map/lib/exts.json", "../../../../../node_modules/lang-map/lib/lang.json", "../../../../../node_modules/lang-map/index.js"], "sourcesContent": ["{\n  \"abap\": [\n    \"abap\"\n  ],\n  \"ags script\": [\n    \"asc\",\n    \"ash\"\n  ],\n  \"ampl\": [\n    \"ampl\"\n  ],\n  \"antlr\": [\n    \"g4\"\n  ],\n  \"apl\": [\n    \"apl\",\n    \"dyalog\"\n  ],\n  \"asp\": [\n    \"asp\",\n    \"asax\",\n    \"ascx\",\n    \"ashx\",\n    \"asmx\",\n    \"aspx\",\n    \"axd\"\n  ],\n  \"ats\": [\n    \"dats\",\n    \"hats\",\n    \"sats\"\n  ],\n  \"actionscript\": [\n    \"as\"\n  ],\n  \"ada\": [\n    \"adb\",\n    \"ada\",\n    \"ads\"\n  ],\n  \"agda\": [\n    \"agda\"\n  ],\n  \"alloy\": [\n    \"als\"\n  ],\n  \"apacheconf\": [\n    \"apacheconf\"\n  ],\n  \"apex\": [\n    \"cls\"\n  ],\n  \"applescript\": [\n    \"applescript\",\n    \"scpt\"\n  ],\n  \"arc\": [\n    \"arc\"\n  ],\n  \"arduino\": [\n    \"ino\"\n  ],\n  \"asciidoc\": [\n    \"asciidoc\",\n    \"adoc\",\n    \"asc\"\n  ],\n  \"aspectj\": [\n    \"aj\"\n  ],\n  \"assembly\": [\n    \"asm\",\n    \"a51\",\n    \"nasm\"\n  ],\n  \"augeas\": [\n    \"aug\"\n  ],\n  \"autohotkey\": [\n    \"ahk\",\n    \"ahkl\"\n  ],\n  \"autoit\": [\n    \"au3\"\n  ],\n  \"awk\": [\n    \"awk\",\n    \"auk\",\n    \"gawk\",\n    \"mawk\",\n    \"nawk\"\n  ],\n  \"batchfile\": [\n    \"bat\",\n    \"cmd\"\n  ],\n  \"befunge\": [\n    \"befunge\"\n  ],\n  \"bison\": [\n    \"y\"\n  ],\n  \"bitbake\": [\n    \"bb\"\n  ],\n  \"blitzbasic\": [\n    \"bb\",\n    \"decls\"\n  ],\n  \"blitzmax\": [\n    \"bmx\"\n  ],\n  \"bluespec\": [\n    \"bsv\"\n  ],\n  \"boo\": [\n    \"boo\"\n  ],\n  \"brainfuck\": [\n    \"b\",\n    \"bf\"\n  ],\n  \"brightscript\": [\n    \"brs\"\n  ],\n  \"bro\": [\n    \"bro\"\n  ],\n  \"c\": [\n    \"c\",\n    \"cats\",\n    \"h\",\n    \"idc\",\n    \"w\"\n  ],\n  \"c#\": [\n    \"cs\",\n    \"cshtml\",\n    \"csx\"\n  ],\n  \"c++\": [\n    \"cpp\",\n    \"c++\",\n    \"cc\",\n    \"cp\",\n    \"cxx\",\n    \"h\",\n    \"h++\",\n    \"hh\",\n    \"hpp\",\n    \"hxx\",\n    \"inl\",\n    \"ipp\",\n    \"tcc\",\n    \"tpp\"\n  ],\n  \"c-objdump\": [\n    \"c-objdump\"\n  ],\n  \"c2hs haskell\": [\n    \"chs\"\n  ],\n  \"clips\": [\n    \"clp\"\n  ],\n  \"cmake\": [\n    \"cmake\",\n    \"cmake.in\"\n  ],\n  \"cobol\": [\n    \"cob\",\n    \"cbl\",\n    \"ccp\",\n    \"cobol\",\n    \"cpy\"\n  ],\n  \"css\": [\n    \"css\"\n  ],\n  \"cap'n proto\": [\n    \"capnp\"\n  ],\n  \"cartocss\": [\n    \"mss\"\n  ],\n  \"ceylon\": [\n    \"ceylon\"\n  ],\n  \"chapel\": [\n    \"chpl\"\n  ],\n  \"chuck\": [\n    \"ck\"\n  ],\n  \"cirru\": [\n    \"cirru\"\n  ],\n  \"clarion\": [\n    \"clw\"\n  ],\n  \"clean\": [\n    \"icl\",\n    \"dcl\"\n  ],\n  \"clojure\": [\n    \"clj\",\n    \"boot\",\n    \"cl2\",\n    \"cljc\",\n    \"cljs\",\n    \"cljs.hl\",\n    \"cljscm\",\n    \"cljx\",\n    \"hic\"\n  ],\n  \"coffeescript\": [\n    \"coffee\",\n    \"_coffee\",\n    \"cjsx\",\n    \"cson\",\n    \"iced\"\n  ],\n  \"coldfusion\": [\n    \"cfm\",\n    \"cfml\"\n  ],\n  \"coldfusion cfc\": [\n    \"cfc\"\n  ],\n  \"common lisp\": [\n    \"lisp\",\n    \"asd\",\n    \"cl\",\n    \"lsp\",\n    \"ny\",\n    \"podsl\"\n  ],\n  \"component pascal\": [\n    \"cp\",\n    \"cps\"\n  ],\n  \"cool\": [\n    \"cl\"\n  ],\n  \"coq\": [\n    \"coq\",\n    \"v\"\n  ],\n  \"cpp-objdump\": [\n    \"cppobjdump\",\n    \"c++-objdump\",\n    \"c++objdump\",\n    \"cpp-objdump\",\n    \"cxx-objdump\"\n  ],\n  \"creole\": [\n    \"creole\"\n  ],\n  \"crystal\": [\n    \"cr\"\n  ],\n  \"cucumber\": [\n    \"feature\"\n  ],\n  \"cuda\": [\n    \"cu\",\n    \"cuh\"\n  ],\n  \"cycript\": [\n    \"cy\"\n  ],\n  \"cython\": [\n    \"pyx\",\n    \"pxd\",\n    \"pxi\"\n  ],\n  \"d\": [\n    \"d\",\n    \"di\"\n  ],\n  \"d-objdump\": [\n    \"d-objdump\"\n  ],\n  \"digital command language\": [\n    \"com\"\n  ],\n  \"dm\": [\n    \"dm\"\n  ],\n  \"dtrace\": [\n    \"d\"\n  ],\n  \"darcs patch\": [\n    \"darcspatch\",\n    \"dpatch\"\n  ],\n  \"dart\": [\n    \"dart\"\n  ],\n  \"diff\": [\n    \"diff\",\n    \"patch\"\n  ],\n  \"dockerfile\": [\n    \"dockerfile\"\n  ],\n  \"dogescript\": [\n    \"djs\"\n  ],\n  \"dylan\": [\n    \"dylan\",\n    \"dyl\",\n    \"intr\",\n    \"lid\"\n  ],\n  \"e\": [\n    \"E\"\n  ],\n  \"ecl\": [\n    \"ecl\",\n    \"eclxml\"\n  ],\n  \"eagle\": [\n    \"sch\",\n    \"brd\"\n  ],\n  \"ecere projects\": [\n    \"epj\"\n  ],\n  \"eiffel\": [\n    \"e\"\n  ],\n  \"elixir\": [\n    \"ex\",\n    \"exs\"\n  ],\n  \"elm\": [\n    \"elm\"\n  ],\n  \"emacs lisp\": [\n    \"el\",\n    \"emacs\",\n    \"emacs.desktop\"\n  ],\n  \"emberscript\": [\n    \"em\",\n    \"emberscript\"\n  ],\n  \"erlang\": [\n    \"erl\",\n    \"es\",\n    \"escript\",\n    \"hrl\"\n  ],\n  \"f#\": [\n    \"fs\",\n    \"fsi\",\n    \"fsx\"\n  ],\n  \"flux\": [\n    \"fx\",\n    \"flux\"\n  ],\n  \"fortran\": [\n    \"f90\",\n    \"f\",\n    \"f03\",\n    \"f08\",\n    \"f77\",\n    \"f95\",\n    \"for\",\n    \"fpp\"\n  ],\n  \"factor\": [\n    \"factor\"\n  ],\n  \"fancy\": [\n    \"fy\",\n    \"fancypack\"\n  ],\n  \"fantom\": [\n    \"fan\"\n  ],\n  \"filterscript\": [\n    \"fs\"\n  ],\n  \"formatted\": [\n    \"for\"\n  ],\n  \"forth\": [\n    \"fth\",\n    \"4th\",\n    \"f\",\n    \"for\",\n    \"forth\",\n    \"fr\",\n    \"frt\",\n    \"fs\"\n  ],\n  \"frege\": [\n    \"fr\"\n  ],\n  \"g-code\": [\n    \"g\",\n    \"gco\",\n    \"gcode\"\n  ],\n  \"gams\": [\n    \"gms\"\n  ],\n  \"gap\": [\n    \"g\",\n    \"gap\",\n    \"gd\",\n    \"gi\",\n    \"tst\"\n  ],\n  \"gas\": [\n    \"s\"\n  ],\n  \"gdscript\": [\n    \"gd\"\n  ],\n  \"glsl\": [\n    \"glsl\",\n    \"fp\",\n    \"frag\",\n    \"frg\",\n    \"fs\",\n    \"fshader\",\n    \"geo\",\n    \"geom\",\n    \"glslv\",\n    \"gshader\",\n    \"shader\",\n    \"vert\",\n    \"vrx\",\n    \"vshader\"\n  ],\n  \"game maker language\": [\n    \"gml\"\n  ],\n  \"genshi\": [\n    \"kid\"\n  ],\n  \"gentoo ebuild\": [\n    \"ebuild\"\n  ],\n  \"gentoo eclass\": [\n    \"eclass\"\n  ],\n  \"gettext catalog\": [\n    \"po\",\n    \"pot\"\n  ],\n  \"glyph\": [\n    \"glf\"\n  ],\n  \"gnuplot\": [\n    \"gp\",\n    \"gnu\",\n    \"gnuplot\",\n    \"plot\",\n    \"plt\"\n  ],\n  \"go\": [\n    \"go\"\n  ],\n  \"golo\": [\n    \"golo\"\n  ],\n  \"gosu\": [\n    \"gs\",\n    \"gst\",\n    \"gsx\",\n    \"vark\"\n  ],\n  \"grace\": [\n    \"grace\"\n  ],\n  \"gradle\": [\n    \"gradle\"\n  ],\n  \"grammatical framework\": [\n    \"gf\"\n  ],\n  \"graph modeling language\": [\n    \"gml\"\n  ],\n  \"graphviz (dot)\": [\n    \"dot\",\n    \"gv\"\n  ],\n  \"groff\": [\n    \"man\",\n    \"1\",\n    \"2\",\n    \"3\",\n    \"4\",\n    \"5\",\n    \"6\",\n    \"7\"\n  ],\n  \"groovy\": [\n    \"groovy\",\n    \"grt\",\n    \"gtpl\",\n    \"gvy\"\n  ],\n  \"groovy server pages\": [\n    \"gsp\"\n  ],\n  \"html\": [\n    \"html\",\n    \"htm\",\n    \"html.hl\",\n    \"st\",\n    \"xht\",\n    \"xhtml\"\n  ],\n  \"html+django\": [\n    \"mustache\",\n    \"jinja\"\n  ],\n  \"html+erb\": [\n    \"erb\",\n    \"erb.deface\"\n  ],\n  \"html+php\": [\n    \"phtml\"\n  ],\n  \"http\": [\n    \"http\"\n  ],\n  \"hack\": [\n    \"hh\",\n    \"php\"\n  ],\n  \"haml\": [\n    \"haml\",\n    \"haml.deface\"\n  ],\n  \"handlebars\": [\n    \"handlebars\",\n    \"hbs\"\n  ],\n  \"harbour\": [\n    \"hb\"\n  ],\n  \"haskell\": [\n    \"hs\",\n    \"hsc\"\n  ],\n  \"haxe\": [\n    \"hx\",\n    \"hxsl\"\n  ],\n  \"hy\": [\n    \"hy\"\n  ],\n  \"idl\": [\n    \"pro\",\n    \"dlm\"\n  ],\n  \"igor pro\": [\n    \"ipf\"\n  ],\n  \"ini\": [\n    \"ini\",\n    \"cfg\",\n    \"prefs\",\n    \"pro\",\n    \"properties\"\n  ],\n  \"irc log\": [\n    \"irclog\",\n    \"weechatlog\"\n  ],\n  \"idris\": [\n    \"idr\",\n    \"lidr\"\n  ],\n  \"inform 7\": [\n    \"ni\",\n    \"i7x\"\n  ],\n  \"inno setup\": [\n    \"iss\"\n  ],\n  \"io\": [\n    \"io\"\n  ],\n  \"ioke\": [\n    \"ik\"\n  ],\n  \"isabelle\": [\n    \"thy\"\n  ],\n  \"j\": [\n    \"ijs\"\n  ],\n  \"jflex\": [\n    \"flex\",\n    \"jflex\"\n  ],\n  \"json\": [\n    \"json\",\n    \"lock\"\n  ],\n  \"json5\": [\n    \"json5\"\n  ],\n  \"jsonld\": [\n    \"jsonld\"\n  ],\n  \"jsoniq\": [\n    \"jq\"\n  ],\n  \"jade\": [\n    \"jade\"\n  ],\n  \"jasmin\": [\n    \"j\"\n  ],\n  \"java\": [\n    \"java\"\n  ],\n  \"java server pages\": [\n    \"jsp\"\n  ],\n  \"javascript\": [\n    \"js\",\n    \"_js\",\n    \"bones\",\n    \"es6\",\n    \"frag\",\n    \"gs\",\n    \"jake\",\n    \"jsb\",\n    \"jsfl\",\n    \"jsm\",\n    \"jss\",\n    \"jsx\",\n    \"njs\",\n    \"pac\",\n    \"sjs\",\n    \"ssjs\",\n    \"sublime-build\",\n    \"sublime-commands\",\n    \"sublime-completions\",\n    \"sublime-keymap\",\n    \"sublime-macro\",\n    \"sublime-menu\",\n    \"sublime-mousemap\",\n    \"sublime-project\",\n    \"sublime-settings\",\n    \"sublime-theme\",\n    \"sublime-workspace\",\n    \"sublime_metrics\",\n    \"sublime_session\",\n    \"xsjs\",\n    \"xsjslib\"\n  ],\n  \"julia\": [\n    \"jl\"\n  ],\n  \"krl\": [\n    \"krl\"\n  ],\n  \"kicad\": [\n    \"sch\"\n  ],\n  \"kit\": [\n    \"kit\"\n  ],\n  \"kotlin\": [\n    \"kt\",\n    \"ktm\",\n    \"kts\"\n  ],\n  \"lfe\": [\n    \"lfe\"\n  ],\n  \"llvm\": [\n    \"ll\"\n  ],\n  \"lolcode\": [\n    \"lol\"\n  ],\n  \"lsl\": [\n    \"lsl\"\n  ],\n  \"labview\": [\n    \"lvproj\"\n  ],\n  \"lasso\": [\n    \"lasso\",\n    \"las\",\n    \"lasso8\",\n    \"lasso9\",\n    \"ldml\"\n  ],\n  \"latte\": [\n    \"latte\"\n  ],\n  \"lean\": [\n    \"lean\",\n    \"hlean\"\n  ],\n  \"less\": [\n    \"less\"\n  ],\n  \"lilypond\": [\n    \"ly\",\n    \"ily\"\n  ],\n  \"limbo\": [\n    \"b\",\n    \"m\"\n  ],\n  \"linker script\": [\n    \"ld\",\n    \"lds\"\n  ],\n  \"liquid\": [\n    \"liquid\"\n  ],\n  \"literate agda\": [\n    \"lagda\"\n  ],\n  \"literate coffeescript\": [\n    \"litcoffee\"\n  ],\n  \"literate haskell\": [\n    \"lhs\"\n  ],\n  \"livescript\": [\n    \"ls\",\n    \"_ls\"\n  ],\n  \"logos\": [\n    \"xm\",\n    \"x\",\n    \"xi\"\n  ],\n  \"logtalk\": [\n    \"lgt\",\n    \"logtalk\"\n  ],\n  \"lookml\": [\n    \"lookml\"\n  ],\n  \"loomscript\": [\n    \"ls\"\n  ],\n  \"lua\": [\n    \"lua\",\n    \"fcgi\",\n    \"nse\",\n    \"pd_lua\",\n    \"rbxs\",\n    \"wlua\"\n  ],\n  \"m\": [\n    \"mumps\",\n    \"m\"\n  ],\n  \"mtml\": [\n    \"mtml\"\n  ],\n  \"muf\": [\n    \"muf\",\n    \"m\"\n  ],\n  \"makefile\": [\n    \"mak\",\n    \"d\",\n    \"mk\"\n  ],\n  \"mako\": [\n    \"mako\",\n    \"mao\"\n  ],\n  \"markdown\": [\n    \"md\",\n    \"markdown\",\n    \"mkd\",\n    \"mkdn\",\n    \"mkdown\",\n    \"ron\"\n  ],\n  \"mask\": [\n    \"mask\"\n  ],\n  \"mathematica\": [\n    \"mathematica\",\n    \"cdf\",\n    \"m\",\n    \"ma\",\n    \"nb\",\n    \"nbp\",\n    \"wl\",\n    \"wlt\"\n  ],\n  \"matlab\": [\n    \"matlab\",\n    \"m\"\n  ],\n  \"max\": [\n    \"maxpat\",\n    \"maxhelp\",\n    \"maxproj\",\n    \"mxt\",\n    \"pat\"\n  ],\n  \"mediawiki\": [\n    \"mediawiki\"\n  ],\n  \"mercury\": [\n    \"m\",\n    \"moo\"\n  ],\n  \"minid\": [\n    \"minid\"\n  ],\n  \"mirah\": [\n    \"druby\",\n    \"duby\",\n    \"mir\",\n    \"mirah\"\n  ],\n  \"modelica\": [\n    \"mo\"\n  ],\n  \"module management system\": [\n    \"mms\",\n    \"mmk\"\n  ],\n  \"monkey\": [\n    \"monkey\"\n  ],\n  \"moocode\": [\n    \"moo\"\n  ],\n  \"moonscript\": [\n    \"moon\"\n  ],\n  \"myghty\": [\n    \"myt\"\n  ],\n  \"nl\": [\n    \"nl\"\n  ],\n  \"nsis\": [\n    \"nsi\",\n    \"nsh\"\n  ],\n  \"nemerle\": [\n    \"n\"\n  ],\n  \"netlinx\": [\n    \"axs\",\n    \"axi\"\n  ],\n  \"netlinx+erb\": [\n    \"axs.erb\",\n    \"axi.erb\"\n  ],\n  \"netlogo\": [\n    \"nlogo\"\n  ],\n  \"newlisp\": [\n    \"nl\",\n    \"lisp\",\n    \"lsp\"\n  ],\n  \"nginx\": [\n    \"nginxconf\"\n  ],\n  \"nimrod\": [\n    \"nim\",\n    \"nimrod\"\n  ],\n  \"ninja\": [\n    \"ninja\"\n  ],\n  \"nit\": [\n    \"nit\"\n  ],\n  \"nix\": [\n    \"nix\"\n  ],\n  \"nu\": [\n    \"nu\"\n  ],\n  \"numpy\": [\n    \"numpy\",\n    \"numpyw\",\n    \"numsc\"\n  ],\n  \"ocaml\": [\n    \"ml\",\n    \"eliom\",\n    \"eliomi\",\n    \"ml4\",\n    \"mli\",\n    \"mll\",\n    \"mly\"\n  ],\n  \"objdump\": [\n    \"objdump\"\n  ],\n  \"objective-c\": [\n    \"m\",\n    \"h\"\n  ],\n  \"objective-c++\": [\n    \"mm\"\n  ],\n  \"objective-j\": [\n    \"j\",\n    \"sj\"\n  ],\n  \"omgrofl\": [\n    \"omgrofl\"\n  ],\n  \"opa\": [\n    \"opa\"\n  ],\n  \"opal\": [\n    \"opal\"\n  ],\n  \"opencl\": [\n    \"cl\",\n    \"opencl\"\n  ],\n  \"openedge abl\": [\n    \"p\",\n    \"cls\"\n  ],\n  \"openscad\": [\n    \"scad\"\n  ],\n  \"org\": [\n    \"org\"\n  ],\n  \"ox\": [\n    \"ox\",\n    \"oxh\",\n    \"oxo\"\n  ],\n  \"oxygene\": [\n    \"oxygene\"\n  ],\n  \"oz\": [\n    \"oz\"\n  ],\n  \"pawn\": [\n    \"pwn\"\n  ],\n  \"php\": [\n    \"php\",\n    \"aw\",\n    \"ctp\",\n    \"fcgi\",\n    \"php3\",\n    \"php4\",\n    \"php5\",\n    \"phpt\"\n  ],\n  \"plsql\": [\n    \"pls\",\n    \"pkb\",\n    \"pks\",\n    \"plb\",\n    \"plsql\",\n    \"sql\"\n  ],\n  \"plpgsql\": [\n    \"sql\"\n  ],\n  \"pan\": [\n    \"pan\"\n  ],\n  \"papyrus\": [\n    \"psc\"\n  ],\n  \"parrot\": [\n    \"parrot\"\n  ],\n  \"parrot assembly\": [\n    \"pasm\"\n  ],\n  \"parrot internal representation\": [\n    \"pir\"\n  ],\n  \"pascal\": [\n    \"pas\",\n    \"dfm\",\n    \"dpr\",\n    \"lpr\",\n    \"pp\"\n  ],\n  \"perl\": [\n    \"pl\",\n    \"cgi\",\n    \"fcgi\",\n    \"perl\",\n    \"ph\",\n    \"plx\",\n    \"pm\",\n    \"pod\",\n    \"psgi\",\n    \"t\"\n  ],\n  \"perl6\": [\n    \"6pl\",\n    \"6pm\",\n    \"nqp\",\n    \"p6\",\n    \"p6l\",\n    \"p6m\",\n    \"pl\",\n    \"pl6\",\n    \"pm\",\n    \"pm6\",\n    \"t\"\n  ],\n  \"piglatin\": [\n    \"pig\"\n  ],\n  \"pike\": [\n    \"pike\",\n    \"pmod\"\n  ],\n  \"pod\": [\n    \"pod\"\n  ],\n  \"pogoscript\": [\n    \"pogo\"\n  ],\n  \"postscript\": [\n    \"ps\",\n    \"eps\"\n  ],\n  \"powershell\": [\n    \"ps1\",\n    \"psd1\",\n    \"psm1\"\n  ],\n  \"processing\": [\n    \"pde\"\n  ],\n  \"prolog\": [\n    \"pl\",\n    \"ecl\",\n    \"pro\",\n    \"prolog\"\n  ],\n  \"propeller spin\": [\n    \"spin\"\n  ],\n  \"protocol buffer\": [\n    \"proto\"\n  ],\n  \"public key\": [\n    \"asc\",\n    \"pub\"\n  ],\n  \"puppet\": [\n    \"pp\"\n  ],\n  \"pure data\": [\n    \"pd\"\n  ],\n  \"purebasic\": [\n    \"pb\",\n    \"pbi\"\n  ],\n  \"purescript\": [\n    \"purs\"\n  ],\n  \"python\": [\n    \"py\",\n    \"cgi\",\n    \"fcgi\",\n    \"gyp\",\n    \"lmi\",\n    \"pyde\",\n    \"pyp\",\n    \"pyt\",\n    \"pyw\",\n    \"tac\",\n    \"wsgi\",\n    \"xpy\"\n  ],\n  \"python traceback\": [\n    \"pytb\"\n  ],\n  \"qml\": [\n    \"qml\"\n  ],\n  \"qmake\": [\n    \"pro\",\n    \"pri\"\n  ],\n  \"r\": [\n    \"r\",\n    \"rd\",\n    \"rsx\"\n  ],\n  \"raml\": [\n    \"raml\"\n  ],\n  \"rdoc\": [\n    \"rdoc\"\n  ],\n  \"realbasic\": [\n    \"rbbas\",\n    \"rbfrm\",\n    \"rbmnu\",\n    \"rbres\",\n    \"rbtbar\",\n    \"rbuistate\"\n  ],\n  \"rhtml\": [\n    \"rhtml\"\n  ],\n  \"rmarkdown\": [\n    \"rmd\"\n  ],\n  \"racket\": [\n    \"rkt\",\n    \"rktd\",\n    \"rktl\",\n    \"scrbl\"\n  ],\n  \"ragel in ruby host\": [\n    \"rl\"\n  ],\n  \"raw token data\": [\n    \"raw\"\n  ],\n  \"rebol\": [\n    \"reb\",\n    \"r\",\n    \"r2\",\n    \"r3\",\n    \"rebol\"\n  ],\n  \"red\": [\n    \"red\",\n    \"reds\"\n  ],\n  \"redcode\": [\n    \"cw\"\n  ],\n  \"renderscript\": [\n    \"rs\",\n    \"rsh\"\n  ],\n  \"robotframework\": [\n    \"robot\"\n  ],\n  \"rouge\": [\n    \"rg\"\n  ],\n  \"ruby\": [\n    \"rb\",\n    \"builder\",\n    \"fcgi\",\n    \"gemspec\",\n    \"god\",\n    \"irbrc\",\n    \"jbuilder\",\n    \"mspec\",\n    \"pluginspec\",\n    \"podspec\",\n    \"rabl\",\n    \"rake\",\n    \"rbuild\",\n    \"rbw\",\n    \"rbx\",\n    \"ru\",\n    \"ruby\",\n    \"thor\",\n    \"watchr\"\n  ],\n  \"rust\": [\n    \"rs\"\n  ],\n  \"sas\": [\n    \"sas\"\n  ],\n  \"scss\": [\n    \"scss\"\n  ],\n  \"sparql\": [\n    \"sparql\",\n    \"rq\"\n  ],\n  \"sqf\": [\n    \"sqf\",\n    \"hqf\"\n  ],\n  \"sql\": [\n    \"sql\",\n    \"cql\",\n    \"ddl\",\n    \"prc\",\n    \"tab\",\n    \"udf\",\n    \"viw\"\n  ],\n  \"sqlpl\": [\n    \"sql\",\n    \"db2\"\n  ],\n  \"ston\": [\n    \"ston\"\n  ],\n  \"svg\": [\n    \"svg\"\n  ],\n  \"sage\": [\n    \"sage\",\n    \"sagews\"\n  ],\n  \"saltstack\": [\n    \"sls\"\n  ],\n  \"sass\": [\n    \"sass\"\n  ],\n  \"scala\": [\n    \"scala\",\n    \"sbt\",\n    \"sc\"\n  ],\n  \"scaml\": [\n    \"scaml\"\n  ],\n  \"scheme\": [\n    \"scm\",\n    \"sld\",\n    \"sls\",\n    \"sps\",\n    \"ss\"\n  ],\n  \"scilab\": [\n    \"sci\",\n    \"sce\",\n    \"tst\"\n  ],\n  \"self\": [\n    \"self\"\n  ],\n  \"shell\": [\n    \"sh\",\n    \"bash\",\n    \"bats\",\n    \"cgi\",\n    \"command\",\n    \"fcgi\",\n    \"ksh\",\n    \"tmux\",\n    \"tool\",\n    \"zsh\"\n  ],\n  \"shellsession\": [\n    \"sh-session\"\n  ],\n  \"shen\": [\n    \"shen\"\n  ],\n  \"slash\": [\n    \"sl\"\n  ],\n  \"slim\": [\n    \"slim\"\n  ],\n  \"smalltalk\": [\n    \"st\",\n    \"cs\"\n  ],\n  \"smarty\": [\n    \"tpl\"\n  ],\n  \"sourcepawn\": [\n    \"sp\",\n    \"sma\"\n  ],\n  \"squirrel\": [\n    \"nut\"\n  ],\n  \"standard ml\": [\n    \"ML\",\n    \"fun\",\n    \"sig\",\n    \"sml\"\n  ],\n  \"stata\": [\n    \"do\",\n    \"ado\",\n    \"doh\",\n    \"ihlp\",\n    \"mata\",\n    \"matah\",\n    \"sthlp\"\n  ],\n  \"stylus\": [\n    \"styl\"\n  ],\n  \"supercollider\": [\n    \"scd\",\n    \"sc\"\n  ],\n  \"swift\": [\n    \"swift\"\n  ],\n  \"systemverilog\": [\n    \"sv\",\n    \"svh\",\n    \"vh\"\n  ],\n  \"toml\": [\n    \"toml\"\n  ],\n  \"txl\": [\n    \"txl\"\n  ],\n  \"tcl\": [\n    \"tcl\",\n    \"adp\",\n    \"tm\"\n  ],\n  \"tcsh\": [\n    \"tcsh\",\n    \"csh\"\n  ],\n  \"tex\": [\n    \"tex\",\n    \"aux\",\n    \"bbx\",\n    \"bib\",\n    \"cbx\",\n    \"cls\",\n    \"dtx\",\n    \"ins\",\n    \"lbx\",\n    \"ltx\",\n    \"mkii\",\n    \"mkiv\",\n    \"mkvi\",\n    \"sty\",\n    \"toc\"\n  ],\n  \"tea\": [\n    \"tea\"\n  ],\n  \"text\": [\n    \"txt\",\n    \"fr\"\n  ],\n  \"textile\": [\n    \"textile\"\n  ],\n  \"thrift\": [\n    \"thrift\"\n  ],\n  \"turing\": [\n    \"t\",\n    \"tu\"\n  ],\n  \"turtle\": [\n    \"ttl\"\n  ],\n  \"twig\": [\n    \"twig\"\n  ],\n  \"typescript\": [\n    \"ts\"\n  ],\n  \"unified parallel c\": [\n    \"upc\"\n  ],\n  \"unrealscript\": [\n    \"uc\"\n  ],\n  \"vcl\": [\n    \"vcl\"\n  ],\n  \"vhdl\": [\n    \"vhdl\",\n    \"vhd\",\n    \"vhf\",\n    \"vhi\",\n    \"vho\",\n    \"vhs\",\n    \"vht\",\n    \"vhw\"\n  ],\n  \"vala\": [\n    \"vala\",\n    \"vapi\"\n  ],\n  \"verilog\": [\n    \"v\",\n    \"veo\"\n  ],\n  \"viml\": [\n    \"vim\"\n  ],\n  \"visual basic\": [\n    \"vb\",\n    \"bas\",\n    \"cls\",\n    \"frm\",\n    \"frx\",\n    \"vba\",\n    \"vbhtml\",\n    \"vbs\"\n  ],\n  \"volt\": [\n    \"volt\"\n  ],\n  \"web ontology language\": [\n    \"owl\"\n  ],\n  \"webidl\": [\n    \"webidl\"\n  ],\n  \"xc\": [\n    \"xc\"\n  ],\n  \"xml\": [\n    \"xml\",\n    \"ant\",\n    \"axml\",\n    \"ccxml\",\n    \"clixml\",\n    \"cproject\",\n    \"csproj\",\n    \"ct\",\n    \"dita\",\n    \"ditamap\",\n    \"ditaval\",\n    \"dll.config\",\n    \"filters\",\n    \"fsproj\",\n    \"fxml\",\n    \"glade\",\n    \"grxml\",\n    \"ivy\",\n    \"jelly\",\n    \"kml\",\n    \"launch\",\n    \"mm\",\n    \"mxml\",\n    \"nproj\",\n    \"nuspec\",\n    \"odd\",\n    \"osm\",\n    \"plist\",\n    \"pluginspec\",\n    \"ps1xml\",\n    \"psc1\",\n    \"pt\",\n    \"rdf\",\n    \"rss\",\n    \"scxml\",\n    \"srdf\",\n    \"storyboard\",\n    \"stTheme\",\n    \"sublime-snippet\",\n    \"targets\",\n    \"tmCommand\",\n    \"tml\",\n    \"tmLanguage\",\n    \"tmPreferences\",\n    \"tmSnippet\",\n    \"tmTheme\",\n    \"ts\",\n    \"ui\",\n    \"urdf\",\n    \"vbproj\",\n    \"vcxproj\",\n    \"vxml\",\n    \"wsdl\",\n    \"wsf\",\n    \"wxi\",\n    \"wxl\",\n    \"wxs\",\n    \"x3d\",\n    \"xacro\",\n    \"xaml\",\n    \"xib\",\n    \"xlf\",\n    \"xliff\",\n    \"xmi\",\n    \"xml.dist\",\n    \"xsd\",\n    \"xul\",\n    \"zcml\"\n  ],\n  \"xproc\": [\n    \"xpl\",\n    \"xproc\"\n  ],\n  \"xquery\": [\n    \"xquery\",\n    \"xq\",\n    \"xql\",\n    \"xqm\",\n    \"xqy\"\n  ],\n  \"xs\": [\n    \"xs\"\n  ],\n  \"xslt\": [\n    \"xslt\",\n    \"xsl\"\n  ],\n  \"xojo\": [\n    \"xojo_code\",\n    \"xojo_menu\",\n    \"xojo_report\",\n    \"xojo_script\",\n    \"xojo_toolbar\",\n    \"xojo_window\"\n  ],\n  \"xtend\": [\n    \"xtend\"\n  ],\n  \"yaml\": [\n    \"yml\",\n    \"reek\",\n    \"rviz\",\n    \"yaml\"\n  ],\n  \"zephir\": [\n    \"zep\"\n  ],\n  \"zimpl\": [\n    \"zimpl\",\n    \"zmpl\",\n    \"zpl\"\n  ],\n  \"desktop\": [\n    \"desktop\",\n    \"desktop.in\"\n  ],\n  \"ec\": [\n    \"ec\",\n    \"eh\"\n  ],\n  \"edn\": [\n    \"edn\"\n  ],\n  \"fish\": [\n    \"fish\"\n  ],\n  \"mupad\": [\n    \"mu\"\n  ],\n  \"nesc\": [\n    \"nc\"\n  ],\n  \"ooc\": [\n    \"ooc\"\n  ],\n  \"restructuredtext\": [\n    \"rst\",\n    \"rest\"\n  ],\n  \"wisp\": [\n    \"wisp\"\n  ],\n  \"xbase\": [\n    \"prg\"\n  ]\n}", "{\n  \"1\": [\n    \"groff\"\n  ],\n  \"2\": [\n    \"groff\"\n  ],\n  \"3\": [\n    \"groff\"\n  ],\n  \"4\": [\n    \"groff\"\n  ],\n  \"5\": [\n    \"groff\"\n  ],\n  \"6\": [\n    \"groff\"\n  ],\n  \"7\": [\n    \"groff\"\n  ],\n  \"abap\": [\n    \"abap\"\n  ],\n  \"asc\": [\n    \"ags script\",\n    \"asciidoc\",\n    \"public key\"\n  ],\n  \"ash\": [\n    \"ags script\"\n  ],\n  \"ampl\": [\n    \"ampl\"\n  ],\n  \"g4\": [\n    \"antlr\"\n  ],\n  \"apl\": [\n    \"apl\"\n  ],\n  \"dyalog\": [\n    \"apl\"\n  ],\n  \"asp\": [\n    \"asp\"\n  ],\n  \"asax\": [\n    \"asp\"\n  ],\n  \"ascx\": [\n    \"asp\"\n  ],\n  \"ashx\": [\n    \"asp\"\n  ],\n  \"asmx\": [\n    \"asp\"\n  ],\n  \"aspx\": [\n    \"asp\"\n  ],\n  \"axd\": [\n    \"asp\"\n  ],\n  \"dats\": [\n    \"ats\"\n  ],\n  \"hats\": [\n    \"ats\"\n  ],\n  \"sats\": [\n    \"ats\"\n  ],\n  \"as\": [\n    \"actionscript\"\n  ],\n  \"adb\": [\n    \"ada\"\n  ],\n  \"ada\": [\n    \"ada\"\n  ],\n  \"ads\": [\n    \"ada\"\n  ],\n  \"agda\": [\n    \"agda\"\n  ],\n  \"als\": [\n    \"alloy\"\n  ],\n  \"apacheconf\": [\n    \"apacheconf\"\n  ],\n  \"cls\": [\n    \"apex\",\n    \"openedge abl\",\n    \"tex\",\n    \"visual basic\"\n  ],\n  \"applescript\": [\n    \"applescript\"\n  ],\n  \"scpt\": [\n    \"applescript\"\n  ],\n  \"arc\": [\n    \"arc\"\n  ],\n  \"ino\": [\n    \"arduino\"\n  ],\n  \"asciidoc\": [\n    \"asciidoc\"\n  ],\n  \"adoc\": [\n    \"asciidoc\"\n  ],\n  \"aj\": [\n    \"aspectj\"\n  ],\n  \"asm\": [\n    \"assembly\"\n  ],\n  \"a51\": [\n    \"assembly\"\n  ],\n  \"nasm\": [\n    \"assembly\"\n  ],\n  \"aug\": [\n    \"augeas\"\n  ],\n  \"ahk\": [\n    \"autohotkey\"\n  ],\n  \"ahkl\": [\n    \"autohotkey\"\n  ],\n  \"au3\": [\n    \"autoit\"\n  ],\n  \"awk\": [\n    \"awk\"\n  ],\n  \"auk\": [\n    \"awk\"\n  ],\n  \"gawk\": [\n    \"awk\"\n  ],\n  \"mawk\": [\n    \"awk\"\n  ],\n  \"nawk\": [\n    \"awk\"\n  ],\n  \"bat\": [\n    \"batchfile\"\n  ],\n  \"cmd\": [\n    \"batchfile\"\n  ],\n  \"befunge\": [\n    \"befunge\"\n  ],\n  \"y\": [\n    \"bison\"\n  ],\n  \"bb\": [\n    \"bitbake\",\n    \"blitzbasic\"\n  ],\n  \"decls\": [\n    \"blitzbasic\"\n  ],\n  \"bmx\": [\n    \"blitzmax\"\n  ],\n  \"bsv\": [\n    \"bluespec\"\n  ],\n  \"boo\": [\n    \"boo\"\n  ],\n  \"b\": [\n    \"brainfuck\",\n    \"limbo\"\n  ],\n  \"bf\": [\n    \"brainfuck\"\n  ],\n  \"brs\": [\n    \"brightscript\"\n  ],\n  \"bro\": [\n    \"bro\"\n  ],\n  \"c\": [\n    \"c\"\n  ],\n  \"cats\": [\n    \"c\"\n  ],\n  \"h\": [\n    \"c\",\n    \"c++\",\n    \"objective-c\"\n  ],\n  \"idc\": [\n    \"c\"\n  ],\n  \"w\": [\n    \"c\"\n  ],\n  \"cs\": [\n    \"c#\",\n    \"smalltalk\"\n  ],\n  \"cshtml\": [\n    \"c#\"\n  ],\n  \"csx\": [\n    \"c#\"\n  ],\n  \"cpp\": [\n    \"c++\"\n  ],\n  \"c++\": [\n    \"c++\"\n  ],\n  \"cc\": [\n    \"c++\"\n  ],\n  \"cp\": [\n    \"c++\",\n    \"component pascal\"\n  ],\n  \"cxx\": [\n    \"c++\"\n  ],\n  \"h++\": [\n    \"c++\"\n  ],\n  \"hh\": [\n    \"c++\",\n    \"hack\"\n  ],\n  \"hpp\": [\n    \"c++\"\n  ],\n  \"hxx\": [\n    \"c++\"\n  ],\n  \"inl\": [\n    \"c++\"\n  ],\n  \"ipp\": [\n    \"c++\"\n  ],\n  \"tcc\": [\n    \"c++\"\n  ],\n  \"tpp\": [\n    \"c++\"\n  ],\n  \"c-objdump\": [\n    \"c-objdump\"\n  ],\n  \"chs\": [\n    \"c2hs haskell\"\n  ],\n  \"clp\": [\n    \"clips\"\n  ],\n  \"cmake\": [\n    \"cmake\"\n  ],\n  \"cmake.in\": [\n    \"cmake\"\n  ],\n  \"cob\": [\n    \"cobol\"\n  ],\n  \"cbl\": [\n    \"cobol\"\n  ],\n  \"ccp\": [\n    \"cobol\"\n  ],\n  \"cobol\": [\n    \"cobol\"\n  ],\n  \"cpy\": [\n    \"cobol\"\n  ],\n  \"css\": [\n    \"css\"\n  ],\n  \"capnp\": [\n    \"cap'n proto\"\n  ],\n  \"mss\": [\n    \"cartocss\"\n  ],\n  \"ceylon\": [\n    \"ceylon\"\n  ],\n  \"chpl\": [\n    \"chapel\"\n  ],\n  \"ck\": [\n    \"chuck\"\n  ],\n  \"cirru\": [\n    \"cirru\"\n  ],\n  \"clw\": [\n    \"clarion\"\n  ],\n  \"icl\": [\n    \"clean\"\n  ],\n  \"dcl\": [\n    \"clean\"\n  ],\n  \"clj\": [\n    \"clojure\"\n  ],\n  \"boot\": [\n    \"clojure\"\n  ],\n  \"cl2\": [\n    \"clojure\"\n  ],\n  \"cljc\": [\n    \"clojure\"\n  ],\n  \"cljs\": [\n    \"clojure\"\n  ],\n  \"cljs.hl\": [\n    \"clojure\"\n  ],\n  \"cljscm\": [\n    \"clojure\"\n  ],\n  \"cljx\": [\n    \"clojure\"\n  ],\n  \"hic\": [\n    \"clojure\"\n  ],\n  \"coffee\": [\n    \"coffeescript\"\n  ],\n  \"_coffee\": [\n    \"coffeescript\"\n  ],\n  \"cjsx\": [\n    \"coffeescript\"\n  ],\n  \"cson\": [\n    \"coffeescript\"\n  ],\n  \"iced\": [\n    \"coffeescript\"\n  ],\n  \"cfm\": [\n    \"coldfusion\"\n  ],\n  \"cfml\": [\n    \"coldfusion\"\n  ],\n  \"cfc\": [\n    \"coldfusion cfc\"\n  ],\n  \"lisp\": [\n    \"common lisp\",\n    \"newlisp\"\n  ],\n  \"asd\": [\n    \"common lisp\"\n  ],\n  \"cl\": [\n    \"common lisp\",\n    \"cool\",\n    \"opencl\"\n  ],\n  \"lsp\": [\n    \"common lisp\",\n    \"newlisp\"\n  ],\n  \"ny\": [\n    \"common lisp\"\n  ],\n  \"podsl\": [\n    \"common lisp\"\n  ],\n  \"cps\": [\n    \"component pascal\"\n  ],\n  \"coq\": [\n    \"coq\"\n  ],\n  \"v\": [\n    \"coq\",\n    \"verilog\"\n  ],\n  \"cppobjdump\": [\n    \"cpp-objdump\"\n  ],\n  \"c++-objdump\": [\n    \"cpp-objdump\"\n  ],\n  \"c++objdump\": [\n    \"cpp-objdump\"\n  ],\n  \"cpp-objdump\": [\n    \"cpp-objdump\"\n  ],\n  \"cxx-objdump\": [\n    \"cpp-objdump\"\n  ],\n  \"creole\": [\n    \"creole\"\n  ],\n  \"cr\": [\n    \"crystal\"\n  ],\n  \"feature\": [\n    \"cucumber\"\n  ],\n  \"cu\": [\n    \"cuda\"\n  ],\n  \"cuh\": [\n    \"cuda\"\n  ],\n  \"cy\": [\n    \"cycript\"\n  ],\n  \"pyx\": [\n    \"cython\"\n  ],\n  \"pxd\": [\n    \"cython\"\n  ],\n  \"pxi\": [\n    \"cython\"\n  ],\n  \"d\": [\n    \"d\",\n    \"dtrace\",\n    \"makefile\"\n  ],\n  \"di\": [\n    \"d\"\n  ],\n  \"d-objdump\": [\n    \"d-objdump\"\n  ],\n  \"com\": [\n    \"digital command language\"\n  ],\n  \"dm\": [\n    \"dm\"\n  ],\n  \"darcspatch\": [\n    \"darcs patch\"\n  ],\n  \"dpatch\": [\n    \"darcs patch\"\n  ],\n  \"dart\": [\n    \"dart\"\n  ],\n  \"diff\": [\n    \"diff\"\n  ],\n  \"patch\": [\n    \"diff\"\n  ],\n  \"dockerfile\": [\n    \"dockerfile\"\n  ],\n  \"djs\": [\n    \"dogescript\"\n  ],\n  \"dylan\": [\n    \"dylan\"\n  ],\n  \"dyl\": [\n    \"dylan\"\n  ],\n  \"intr\": [\n    \"dylan\"\n  ],\n  \"lid\": [\n    \"dylan\"\n  ],\n  \"E\": [\n    \"e\"\n  ],\n  \"ecl\": [\n    \"ecl\",\n    \"prolog\"\n  ],\n  \"eclxml\": [\n    \"ecl\"\n  ],\n  \"sch\": [\n    \"eagle\",\n    \"kicad\"\n  ],\n  \"brd\": [\n    \"eagle\"\n  ],\n  \"epj\": [\n    \"ecere projects\"\n  ],\n  \"e\": [\n    \"eiffel\"\n  ],\n  \"ex\": [\n    \"elixir\"\n  ],\n  \"exs\": [\n    \"elixir\"\n  ],\n  \"elm\": [\n    \"elm\"\n  ],\n  \"el\": [\n    \"emacs lisp\"\n  ],\n  \"emacs\": [\n    \"emacs lisp\"\n  ],\n  \"emacs.desktop\": [\n    \"emacs lisp\"\n  ],\n  \"em\": [\n    \"emberscript\"\n  ],\n  \"emberscript\": [\n    \"emberscript\"\n  ],\n  \"erl\": [\n    \"erlang\"\n  ],\n  \"es\": [\n    \"erlang\"\n  ],\n  \"escript\": [\n    \"erlang\"\n  ],\n  \"hrl\": [\n    \"erlang\"\n  ],\n  \"fs\": [\n    \"f#\",\n    \"filterscript\",\n    \"forth\",\n    \"glsl\"\n  ],\n  \"fsi\": [\n    \"f#\"\n  ],\n  \"fsx\": [\n    \"f#\"\n  ],\n  \"fx\": [\n    \"flux\"\n  ],\n  \"flux\": [\n    \"flux\"\n  ],\n  \"f90\": [\n    \"fortran\"\n  ],\n  \"f\": [\n    \"fortran\",\n    \"forth\"\n  ],\n  \"f03\": [\n    \"fortran\"\n  ],\n  \"f08\": [\n    \"fortran\"\n  ],\n  \"f77\": [\n    \"fortran\"\n  ],\n  \"f95\": [\n    \"fortran\"\n  ],\n  \"for\": [\n    \"fortran\",\n    \"formatted\",\n    \"forth\"\n  ],\n  \"fpp\": [\n    \"fortran\"\n  ],\n  \"factor\": [\n    \"factor\"\n  ],\n  \"fy\": [\n    \"fancy\"\n  ],\n  \"fancypack\": [\n    \"fancy\"\n  ],\n  \"fan\": [\n    \"fantom\"\n  ],\n  \"fth\": [\n    \"forth\"\n  ],\n  \"4th\": [\n    \"forth\"\n  ],\n  \"forth\": [\n    \"forth\"\n  ],\n  \"fr\": [\n    \"forth\",\n    \"frege\",\n    \"text\"\n  ],\n  \"frt\": [\n    \"forth\"\n  ],\n  \"g\": [\n    \"g-code\",\n    \"gap\"\n  ],\n  \"gco\": [\n    \"g-code\"\n  ],\n  \"gcode\": [\n    \"g-code\"\n  ],\n  \"gms\": [\n    \"gams\"\n  ],\n  \"gap\": [\n    \"gap\"\n  ],\n  \"gd\": [\n    \"gap\",\n    \"gdscript\"\n  ],\n  \"gi\": [\n    \"gap\"\n  ],\n  \"tst\": [\n    \"gap\",\n    \"scilab\"\n  ],\n  \"s\": [\n    \"gas\"\n  ],\n  \"glsl\": [\n    \"glsl\"\n  ],\n  \"fp\": [\n    \"glsl\"\n  ],\n  \"frag\": [\n    \"glsl\",\n    \"javascript\"\n  ],\n  \"frg\": [\n    \"glsl\"\n  ],\n  \"fshader\": [\n    \"glsl\"\n  ],\n  \"geo\": [\n    \"glsl\"\n  ],\n  \"geom\": [\n    \"glsl\"\n  ],\n  \"glslv\": [\n    \"glsl\"\n  ],\n  \"gshader\": [\n    \"glsl\"\n  ],\n  \"shader\": [\n    \"glsl\"\n  ],\n  \"vert\": [\n    \"glsl\"\n  ],\n  \"vrx\": [\n    \"glsl\"\n  ],\n  \"vshader\": [\n    \"glsl\"\n  ],\n  \"gml\": [\n    \"game maker language\",\n    \"graph modeling language\"\n  ],\n  \"kid\": [\n    \"genshi\"\n  ],\n  \"ebuild\": [\n    \"gentoo ebuild\"\n  ],\n  \"eclass\": [\n    \"gentoo eclass\"\n  ],\n  \"po\": [\n    \"gettext catalog\"\n  ],\n  \"pot\": [\n    \"gettext catalog\"\n  ],\n  \"glf\": [\n    \"glyph\"\n  ],\n  \"gp\": [\n    \"gnuplot\"\n  ],\n  \"gnu\": [\n    \"gnuplot\"\n  ],\n  \"gnuplot\": [\n    \"gnuplot\"\n  ],\n  \"plot\": [\n    \"gnuplot\"\n  ],\n  \"plt\": [\n    \"gnuplot\"\n  ],\n  \"go\": [\n    \"go\"\n  ],\n  \"golo\": [\n    \"golo\"\n  ],\n  \"gs\": [\n    \"gosu\",\n    \"javascript\"\n  ],\n  \"gst\": [\n    \"gosu\"\n  ],\n  \"gsx\": [\n    \"gosu\"\n  ],\n  \"vark\": [\n    \"gosu\"\n  ],\n  \"grace\": [\n    \"grace\"\n  ],\n  \"gradle\": [\n    \"gradle\"\n  ],\n  \"gf\": [\n    \"grammatical framework\"\n  ],\n  \"dot\": [\n    \"graphviz (dot)\"\n  ],\n  \"gv\": [\n    \"graphviz (dot)\"\n  ],\n  \"man\": [\n    \"groff\"\n  ],\n  \"groovy\": [\n    \"groovy\"\n  ],\n  \"grt\": [\n    \"groovy\"\n  ],\n  \"gtpl\": [\n    \"groovy\"\n  ],\n  \"gvy\": [\n    \"groovy\"\n  ],\n  \"gsp\": [\n    \"groovy server pages\"\n  ],\n  \"html\": [\n    \"html\"\n  ],\n  \"htm\": [\n    \"html\"\n  ],\n  \"html.hl\": [\n    \"html\"\n  ],\n  \"st\": [\n    \"html\",\n    \"smalltalk\"\n  ],\n  \"xht\": [\n    \"html\"\n  ],\n  \"xhtml\": [\n    \"html\"\n  ],\n  \"mustache\": [\n    \"html+django\"\n  ],\n  \"jinja\": [\n    \"html+django\"\n  ],\n  \"erb\": [\n    \"html+erb\"\n  ],\n  \"erb.deface\": [\n    \"html+erb\"\n  ],\n  \"phtml\": [\n    \"html+php\"\n  ],\n  \"http\": [\n    \"http\"\n  ],\n  \"php\": [\n    \"hack\",\n    \"php\"\n  ],\n  \"haml\": [\n    \"haml\"\n  ],\n  \"haml.deface\": [\n    \"haml\"\n  ],\n  \"handlebars\": [\n    \"handlebars\"\n  ],\n  \"hbs\": [\n    \"handlebars\"\n  ],\n  \"hb\": [\n    \"harbour\"\n  ],\n  \"hs\": [\n    \"haskell\"\n  ],\n  \"hsc\": [\n    \"haskell\"\n  ],\n  \"hx\": [\n    \"haxe\"\n  ],\n  \"hxsl\": [\n    \"haxe\"\n  ],\n  \"hy\": [\n    \"hy\"\n  ],\n  \"pro\": [\n    \"idl\",\n    \"ini\",\n    \"prolog\",\n    \"qmake\"\n  ],\n  \"dlm\": [\n    \"idl\"\n  ],\n  \"ipf\": [\n    \"igor pro\"\n  ],\n  \"ini\": [\n    \"ini\"\n  ],\n  \"cfg\": [\n    \"ini\"\n  ],\n  \"prefs\": [\n    \"ini\"\n  ],\n  \"properties\": [\n    \"ini\"\n  ],\n  \"irclog\": [\n    \"irc log\"\n  ],\n  \"weechatlog\": [\n    \"irc log\"\n  ],\n  \"idr\": [\n    \"idris\"\n  ],\n  \"lidr\": [\n    \"idris\"\n  ],\n  \"ni\": [\n    \"inform 7\"\n  ],\n  \"i7x\": [\n    \"inform 7\"\n  ],\n  \"iss\": [\n    \"inno setup\"\n  ],\n  \"io\": [\n    \"io\"\n  ],\n  \"ik\": [\n    \"ioke\"\n  ],\n  \"thy\": [\n    \"isabelle\"\n  ],\n  \"ijs\": [\n    \"j\"\n  ],\n  \"flex\": [\n    \"jflex\"\n  ],\n  \"jflex\": [\n    \"jflex\"\n  ],\n  \"json\": [\n    \"json\"\n  ],\n  \"lock\": [\n    \"json\"\n  ],\n  \"json5\": [\n    \"json5\"\n  ],\n  \"jsonld\": [\n    \"jsonld\"\n  ],\n  \"jq\": [\n    \"jsoniq\"\n  ],\n  \"jade\": [\n    \"jade\"\n  ],\n  \"j\": [\n    \"jasmin\",\n    \"objective-j\"\n  ],\n  \"java\": [\n    \"java\"\n  ],\n  \"jsp\": [\n    \"java server pages\"\n  ],\n  \"js\": [\n    \"javascript\"\n  ],\n  \"_js\": [\n    \"javascript\"\n  ],\n  \"bones\": [\n    \"javascript\"\n  ],\n  \"es6\": [\n    \"javascript\"\n  ],\n  \"jake\": [\n    \"javascript\"\n  ],\n  \"jsb\": [\n    \"javascript\"\n  ],\n  \"jsfl\": [\n    \"javascript\"\n  ],\n  \"jsm\": [\n    \"javascript\"\n  ],\n  \"jss\": [\n    \"javascript\"\n  ],\n  \"jsx\": [\n    \"javascript\"\n  ],\n  \"njs\": [\n    \"javascript\"\n  ],\n  \"pac\": [\n    \"javascript\"\n  ],\n  \"sjs\": [\n    \"javascript\"\n  ],\n  \"ssjs\": [\n    \"javascript\"\n  ],\n  \"sublime-build\": [\n    \"javascript\"\n  ],\n  \"sublime-commands\": [\n    \"javascript\"\n  ],\n  \"sublime-completions\": [\n    \"javascript\"\n  ],\n  \"sublime-keymap\": [\n    \"javascript\"\n  ],\n  \"sublime-macro\": [\n    \"javascript\"\n  ],\n  \"sublime-menu\": [\n    \"javascript\"\n  ],\n  \"sublime-mousemap\": [\n    \"javascript\"\n  ],\n  \"sublime-project\": [\n    \"javascript\"\n  ],\n  \"sublime-settings\": [\n    \"javascript\"\n  ],\n  \"sublime-theme\": [\n    \"javascript\"\n  ],\n  \"sublime-workspace\": [\n    \"javascript\"\n  ],\n  \"sublime_metrics\": [\n    \"javascript\"\n  ],\n  \"sublime_session\": [\n    \"javascript\"\n  ],\n  \"xsjs\": [\n    \"javascript\"\n  ],\n  \"xsjslib\": [\n    \"javascript\"\n  ],\n  \"jl\": [\n    \"julia\"\n  ],\n  \"krl\": [\n    \"krl\"\n  ],\n  \"kit\": [\n    \"kit\"\n  ],\n  \"kt\": [\n    \"kotlin\"\n  ],\n  \"ktm\": [\n    \"kotlin\"\n  ],\n  \"kts\": [\n    \"kotlin\"\n  ],\n  \"lfe\": [\n    \"lfe\"\n  ],\n  \"ll\": [\n    \"llvm\"\n  ],\n  \"lol\": [\n    \"lolcode\"\n  ],\n  \"lsl\": [\n    \"lsl\"\n  ],\n  \"lvproj\": [\n    \"labview\"\n  ],\n  \"lasso\": [\n    \"lasso\"\n  ],\n  \"las\": [\n    \"lasso\"\n  ],\n  \"lasso8\": [\n    \"lasso\"\n  ],\n  \"lasso9\": [\n    \"lasso\"\n  ],\n  \"ldml\": [\n    \"lasso\"\n  ],\n  \"latte\": [\n    \"latte\"\n  ],\n  \"lean\": [\n    \"lean\"\n  ],\n  \"hlean\": [\n    \"lean\"\n  ],\n  \"less\": [\n    \"less\"\n  ],\n  \"ly\": [\n    \"lilypond\"\n  ],\n  \"ily\": [\n    \"lilypond\"\n  ],\n  \"m\": [\n    \"limbo\",\n    \"m\",\n    \"muf\",\n    \"mathematica\",\n    \"matlab\",\n    \"mercury\",\n    \"objective-c\"\n  ],\n  \"ld\": [\n    \"linker script\"\n  ],\n  \"lds\": [\n    \"linker script\"\n  ],\n  \"liquid\": [\n    \"liquid\"\n  ],\n  \"lagda\": [\n    \"literate agda\"\n  ],\n  \"litcoffee\": [\n    \"literate coffeescript\"\n  ],\n  \"lhs\": [\n    \"literate haskell\"\n  ],\n  \"ls\": [\n    \"livescript\",\n    \"loomscript\"\n  ],\n  \"_ls\": [\n    \"livescript\"\n  ],\n  \"xm\": [\n    \"logos\"\n  ],\n  \"x\": [\n    \"logos\"\n  ],\n  \"xi\": [\n    \"logos\"\n  ],\n  \"lgt\": [\n    \"logtalk\"\n  ],\n  \"logtalk\": [\n    \"logtalk\"\n  ],\n  \"lookml\": [\n    \"lookml\"\n  ],\n  \"lua\": [\n    \"lua\"\n  ],\n  \"fcgi\": [\n    \"lua\",\n    \"php\",\n    \"perl\",\n    \"python\",\n    \"ruby\",\n    \"shell\"\n  ],\n  \"nse\": [\n    \"lua\"\n  ],\n  \"pd_lua\": [\n    \"lua\"\n  ],\n  \"rbxs\": [\n    \"lua\"\n  ],\n  \"wlua\": [\n    \"lua\"\n  ],\n  \"mumps\": [\n    \"m\"\n  ],\n  \"mtml\": [\n    \"mtml\"\n  ],\n  \"muf\": [\n    \"muf\"\n  ],\n  \"mak\": [\n    \"makefile\"\n  ],\n  \"mk\": [\n    \"makefile\"\n  ],\n  \"mako\": [\n    \"mako\"\n  ],\n  \"mao\": [\n    \"mako\"\n  ],\n  \"md\": [\n    \"markdown\"\n  ],\n  \"markdown\": [\n    \"markdown\"\n  ],\n  \"mkd\": [\n    \"markdown\"\n  ],\n  \"mkdn\": [\n    \"markdown\"\n  ],\n  \"mkdown\": [\n    \"markdown\"\n  ],\n  \"ron\": [\n    \"markdown\"\n  ],\n  \"mask\": [\n    \"mask\"\n  ],\n  \"mathematica\": [\n    \"mathematica\"\n  ],\n  \"cdf\": [\n    \"mathematica\"\n  ],\n  \"ma\": [\n    \"mathematica\"\n  ],\n  \"nb\": [\n    \"mathematica\"\n  ],\n  \"nbp\": [\n    \"mathematica\"\n  ],\n  \"wl\": [\n    \"mathematica\"\n  ],\n  \"wlt\": [\n    \"mathematica\"\n  ],\n  \"matlab\": [\n    \"matlab\"\n  ],\n  \"maxpat\": [\n    \"max\"\n  ],\n  \"maxhelp\": [\n    \"max\"\n  ],\n  \"maxproj\": [\n    \"max\"\n  ],\n  \"mxt\": [\n    \"max\"\n  ],\n  \"pat\": [\n    \"max\"\n  ],\n  \"mediawiki\": [\n    \"mediawiki\"\n  ],\n  \"moo\": [\n    \"mercury\",\n    \"moocode\"\n  ],\n  \"minid\": [\n    \"minid\"\n  ],\n  \"druby\": [\n    \"mirah\"\n  ],\n  \"duby\": [\n    \"mirah\"\n  ],\n  \"mir\": [\n    \"mirah\"\n  ],\n  \"mirah\": [\n    \"mirah\"\n  ],\n  \"mo\": [\n    \"modelica\"\n  ],\n  \"mms\": [\n    \"module management system\"\n  ],\n  \"mmk\": [\n    \"module management system\"\n  ],\n  \"monkey\": [\n    \"monkey\"\n  ],\n  \"moon\": [\n    \"moonscript\"\n  ],\n  \"myt\": [\n    \"myghty\"\n  ],\n  \"nl\": [\n    \"nl\",\n    \"newlisp\"\n  ],\n  \"nsi\": [\n    \"nsis\"\n  ],\n  \"nsh\": [\n    \"nsis\"\n  ],\n  \"n\": [\n    \"nemerle\"\n  ],\n  \"axs\": [\n    \"netlinx\"\n  ],\n  \"axi\": [\n    \"netlinx\"\n  ],\n  \"axs.erb\": [\n    \"netlinx+erb\"\n  ],\n  \"axi.erb\": [\n    \"netlinx+erb\"\n  ],\n  \"nlogo\": [\n    \"netlogo\"\n  ],\n  \"nginxconf\": [\n    \"nginx\"\n  ],\n  \"nim\": [\n    \"nimrod\"\n  ],\n  \"nimrod\": [\n    \"nimrod\"\n  ],\n  \"ninja\": [\n    \"ninja\"\n  ],\n  \"nit\": [\n    \"nit\"\n  ],\n  \"nix\": [\n    \"nix\"\n  ],\n  \"nu\": [\n    \"nu\"\n  ],\n  \"numpy\": [\n    \"numpy\"\n  ],\n  \"numpyw\": [\n    \"numpy\"\n  ],\n  \"numsc\": [\n    \"numpy\"\n  ],\n  \"ml\": [\n    \"ocaml\"\n  ],\n  \"eliom\": [\n    \"ocaml\"\n  ],\n  \"eliomi\": [\n    \"ocaml\"\n  ],\n  \"ml4\": [\n    \"ocaml\"\n  ],\n  \"mli\": [\n    \"ocaml\"\n  ],\n  \"mll\": [\n    \"ocaml\"\n  ],\n  \"mly\": [\n    \"ocaml\"\n  ],\n  \"objdump\": [\n    \"objdump\"\n  ],\n  \"mm\": [\n    \"objective-c++\",\n    \"xml\"\n  ],\n  \"sj\": [\n    \"objective-j\"\n  ],\n  \"omgrofl\": [\n    \"omgrofl\"\n  ],\n  \"opa\": [\n    \"opa\"\n  ],\n  \"opal\": [\n    \"opal\"\n  ],\n  \"opencl\": [\n    \"opencl\"\n  ],\n  \"p\": [\n    \"openedge abl\"\n  ],\n  \"scad\": [\n    \"openscad\"\n  ],\n  \"org\": [\n    \"org\"\n  ],\n  \"ox\": [\n    \"ox\"\n  ],\n  \"oxh\": [\n    \"ox\"\n  ],\n  \"oxo\": [\n    \"ox\"\n  ],\n  \"oxygene\": [\n    \"oxygene\"\n  ],\n  \"oz\": [\n    \"oz\"\n  ],\n  \"pwn\": [\n    \"pawn\"\n  ],\n  \"aw\": [\n    \"php\"\n  ],\n  \"ctp\": [\n    \"php\"\n  ],\n  \"php3\": [\n    \"php\"\n  ],\n  \"php4\": [\n    \"php\"\n  ],\n  \"php5\": [\n    \"php\"\n  ],\n  \"phpt\": [\n    \"php\"\n  ],\n  \"pls\": [\n    \"plsql\"\n  ],\n  \"pkb\": [\n    \"plsql\"\n  ],\n  \"pks\": [\n    \"plsql\"\n  ],\n  \"plb\": [\n    \"plsql\"\n  ],\n  \"plsql\": [\n    \"plsql\"\n  ],\n  \"sql\": [\n    \"plsql\",\n    \"plpgsql\",\n    \"sql\",\n    \"sqlpl\"\n  ],\n  \"pan\": [\n    \"pan\"\n  ],\n  \"psc\": [\n    \"papyrus\"\n  ],\n  \"parrot\": [\n    \"parrot\"\n  ],\n  \"pasm\": [\n    \"parrot assembly\"\n  ],\n  \"pir\": [\n    \"parrot internal representation\"\n  ],\n  \"pas\": [\n    \"pascal\"\n  ],\n  \"dfm\": [\n    \"pascal\"\n  ],\n  \"dpr\": [\n    \"pascal\"\n  ],\n  \"lpr\": [\n    \"pascal\"\n  ],\n  \"pp\": [\n    \"pascal\",\n    \"puppet\"\n  ],\n  \"pl\": [\n    \"perl\",\n    \"perl6\",\n    \"prolog\"\n  ],\n  \"cgi\": [\n    \"perl\",\n    \"python\",\n    \"shell\"\n  ],\n  \"perl\": [\n    \"perl\"\n  ],\n  \"ph\": [\n    \"perl\"\n  ],\n  \"plx\": [\n    \"perl\"\n  ],\n  \"pm\": [\n    \"perl\",\n    \"perl6\"\n  ],\n  \"pod\": [\n    \"perl\",\n    \"pod\"\n  ],\n  \"psgi\": [\n    \"perl\"\n  ],\n  \"t\": [\n    \"perl\",\n    \"perl6\",\n    \"turing\"\n  ],\n  \"6pl\": [\n    \"perl6\"\n  ],\n  \"6pm\": [\n    \"perl6\"\n  ],\n  \"nqp\": [\n    \"perl6\"\n  ],\n  \"p6\": [\n    \"perl6\"\n  ],\n  \"p6l\": [\n    \"perl6\"\n  ],\n  \"p6m\": [\n    \"perl6\"\n  ],\n  \"pl6\": [\n    \"perl6\"\n  ],\n  \"pm6\": [\n    \"perl6\"\n  ],\n  \"pig\": [\n    \"piglatin\"\n  ],\n  \"pike\": [\n    \"pike\"\n  ],\n  \"pmod\": [\n    \"pike\"\n  ],\n  \"pogo\": [\n    \"pogoscript\"\n  ],\n  \"ps\": [\n    \"postscript\"\n  ],\n  \"eps\": [\n    \"postscript\"\n  ],\n  \"ps1\": [\n    \"powershell\"\n  ],\n  \"psd1\": [\n    \"powershell\"\n  ],\n  \"psm1\": [\n    \"powershell\"\n  ],\n  \"pde\": [\n    \"processing\"\n  ],\n  \"prolog\": [\n    \"prolog\"\n  ],\n  \"spin\": [\n    \"propeller spin\"\n  ],\n  \"proto\": [\n    \"protocol buffer\"\n  ],\n  \"pub\": [\n    \"public key\"\n  ],\n  \"pd\": [\n    \"pure data\"\n  ],\n  \"pb\": [\n    \"purebasic\"\n  ],\n  \"pbi\": [\n    \"purebasic\"\n  ],\n  \"purs\": [\n    \"purescript\"\n  ],\n  \"py\": [\n    \"python\"\n  ],\n  \"gyp\": [\n    \"python\"\n  ],\n  \"lmi\": [\n    \"python\"\n  ],\n  \"pyde\": [\n    \"python\"\n  ],\n  \"pyp\": [\n    \"python\"\n  ],\n  \"pyt\": [\n    \"python\"\n  ],\n  \"pyw\": [\n    \"python\"\n  ],\n  \"tac\": [\n    \"python\"\n  ],\n  \"wsgi\": [\n    \"python\"\n  ],\n  \"xpy\": [\n    \"python\"\n  ],\n  \"pytb\": [\n    \"python traceback\"\n  ],\n  \"qml\": [\n    \"qml\"\n  ],\n  \"pri\": [\n    \"qmake\"\n  ],\n  \"r\": [\n    \"r\",\n    \"rebol\"\n  ],\n  \"rd\": [\n    \"r\"\n  ],\n  \"rsx\": [\n    \"r\"\n  ],\n  \"raml\": [\n    \"raml\"\n  ],\n  \"rdoc\": [\n    \"rdoc\"\n  ],\n  \"rbbas\": [\n    \"realbasic\"\n  ],\n  \"rbfrm\": [\n    \"realbasic\"\n  ],\n  \"rbmnu\": [\n    \"realbasic\"\n  ],\n  \"rbres\": [\n    \"realbasic\"\n  ],\n  \"rbtbar\": [\n    \"realbasic\"\n  ],\n  \"rbuistate\": [\n    \"realbasic\"\n  ],\n  \"rhtml\": [\n    \"rhtml\"\n  ],\n  \"rmd\": [\n    \"rmarkdown\"\n  ],\n  \"rkt\": [\n    \"racket\"\n  ],\n  \"rktd\": [\n    \"racket\"\n  ],\n  \"rktl\": [\n    \"racket\"\n  ],\n  \"scrbl\": [\n    \"racket\"\n  ],\n  \"rl\": [\n    \"ragel in ruby host\"\n  ],\n  \"raw\": [\n    \"raw token data\"\n  ],\n  \"reb\": [\n    \"rebol\"\n  ],\n  \"r2\": [\n    \"rebol\"\n  ],\n  \"r3\": [\n    \"rebol\"\n  ],\n  \"rebol\": [\n    \"rebol\"\n  ],\n  \"red\": [\n    \"red\"\n  ],\n  \"reds\": [\n    \"red\"\n  ],\n  \"cw\": [\n    \"redcode\"\n  ],\n  \"rs\": [\n    \"renderscript\",\n    \"rust\"\n  ],\n  \"rsh\": [\n    \"renderscript\"\n  ],\n  \"robot\": [\n    \"robotframework\"\n  ],\n  \"rg\": [\n    \"rouge\"\n  ],\n  \"rb\": [\n    \"ruby\"\n  ],\n  \"builder\": [\n    \"ruby\"\n  ],\n  \"gemspec\": [\n    \"ruby\"\n  ],\n  \"god\": [\n    \"ruby\"\n  ],\n  \"irbrc\": [\n    \"ruby\"\n  ],\n  \"jbuilder\": [\n    \"ruby\"\n  ],\n  \"mspec\": [\n    \"ruby\"\n  ],\n  \"pluginspec\": [\n    \"ruby\",\n    \"xml\"\n  ],\n  \"podspec\": [\n    \"ruby\"\n  ],\n  \"rabl\": [\n    \"ruby\"\n  ],\n  \"rake\": [\n    \"ruby\"\n  ],\n  \"rbuild\": [\n    \"ruby\"\n  ],\n  \"rbw\": [\n    \"ruby\"\n  ],\n  \"rbx\": [\n    \"ruby\"\n  ],\n  \"ru\": [\n    \"ruby\"\n  ],\n  \"ruby\": [\n    \"ruby\"\n  ],\n  \"thor\": [\n    \"ruby\"\n  ],\n  \"watchr\": [\n    \"ruby\"\n  ],\n  \"sas\": [\n    \"sas\"\n  ],\n  \"scss\": [\n    \"scss\"\n  ],\n  \"sparql\": [\n    \"sparql\"\n  ],\n  \"rq\": [\n    \"sparql\"\n  ],\n  \"sqf\": [\n    \"sqf\"\n  ],\n  \"hqf\": [\n    \"sqf\"\n  ],\n  \"cql\": [\n    \"sql\"\n  ],\n  \"ddl\": [\n    \"sql\"\n  ],\n  \"prc\": [\n    \"sql\"\n  ],\n  \"tab\": [\n    \"sql\"\n  ],\n  \"udf\": [\n    \"sql\"\n  ],\n  \"viw\": [\n    \"sql\"\n  ],\n  \"db2\": [\n    \"sqlpl\"\n  ],\n  \"ston\": [\n    \"ston\"\n  ],\n  \"svg\": [\n    \"svg\"\n  ],\n  \"sage\": [\n    \"sage\"\n  ],\n  \"sagews\": [\n    \"sage\"\n  ],\n  \"sls\": [\n    \"saltstack\",\n    \"scheme\"\n  ],\n  \"sass\": [\n    \"sass\"\n  ],\n  \"scala\": [\n    \"scala\"\n  ],\n  \"sbt\": [\n    \"scala\"\n  ],\n  \"sc\": [\n    \"scala\",\n    \"supercollider\"\n  ],\n  \"scaml\": [\n    \"scaml\"\n  ],\n  \"scm\": [\n    \"scheme\"\n  ],\n  \"sld\": [\n    \"scheme\"\n  ],\n  \"sps\": [\n    \"scheme\"\n  ],\n  \"ss\": [\n    \"scheme\"\n  ],\n  \"sci\": [\n    \"scilab\"\n  ],\n  \"sce\": [\n    \"scilab\"\n  ],\n  \"self\": [\n    \"self\"\n  ],\n  \"sh\": [\n    \"shell\"\n  ],\n  \"bash\": [\n    \"shell\"\n  ],\n  \"bats\": [\n    \"shell\"\n  ],\n  \"command\": [\n    \"shell\"\n  ],\n  \"ksh\": [\n    \"shell\"\n  ],\n  \"tmux\": [\n    \"shell\"\n  ],\n  \"tool\": [\n    \"shell\"\n  ],\n  \"zsh\": [\n    \"shell\"\n  ],\n  \"sh-session\": [\n    \"shellsession\"\n  ],\n  \"shen\": [\n    \"shen\"\n  ],\n  \"sl\": [\n    \"slash\"\n  ],\n  \"slim\": [\n    \"slim\"\n  ],\n  \"tpl\": [\n    \"smarty\"\n  ],\n  \"sp\": [\n    \"sourcepawn\"\n  ],\n  \"sma\": [\n    \"sourcepawn\"\n  ],\n  \"nut\": [\n    \"squirrel\"\n  ],\n  \"ML\": [\n    \"standard ml\"\n  ],\n  \"fun\": [\n    \"standard ml\"\n  ],\n  \"sig\": [\n    \"standard ml\"\n  ],\n  \"sml\": [\n    \"standard ml\"\n  ],\n  \"do\": [\n    \"stata\"\n  ],\n  \"ado\": [\n    \"stata\"\n  ],\n  \"doh\": [\n    \"stata\"\n  ],\n  \"ihlp\": [\n    \"stata\"\n  ],\n  \"mata\": [\n    \"stata\"\n  ],\n  \"matah\": [\n    \"stata\"\n  ],\n  \"sthlp\": [\n    \"stata\"\n  ],\n  \"styl\": [\n    \"stylus\"\n  ],\n  \"scd\": [\n    \"supercollider\"\n  ],\n  \"swift\": [\n    \"swift\"\n  ],\n  \"sv\": [\n    \"systemverilog\"\n  ],\n  \"svh\": [\n    \"systemverilog\"\n  ],\n  \"vh\": [\n    \"systemverilog\"\n  ],\n  \"toml\": [\n    \"toml\"\n  ],\n  \"txl\": [\n    \"txl\"\n  ],\n  \"tcl\": [\n    \"tcl\"\n  ],\n  \"adp\": [\n    \"tcl\"\n  ],\n  \"tm\": [\n    \"tcl\"\n  ],\n  \"tcsh\": [\n    \"tcsh\"\n  ],\n  \"csh\": [\n    \"tcsh\"\n  ],\n  \"tex\": [\n    \"tex\"\n  ],\n  \"aux\": [\n    \"tex\"\n  ],\n  \"bbx\": [\n    \"tex\"\n  ],\n  \"bib\": [\n    \"tex\"\n  ],\n  \"cbx\": [\n    \"tex\"\n  ],\n  \"dtx\": [\n    \"tex\"\n  ],\n  \"ins\": [\n    \"tex\"\n  ],\n  \"lbx\": [\n    \"tex\"\n  ],\n  \"ltx\": [\n    \"tex\"\n  ],\n  \"mkii\": [\n    \"tex\"\n  ],\n  \"mkiv\": [\n    \"tex\"\n  ],\n  \"mkvi\": [\n    \"tex\"\n  ],\n  \"sty\": [\n    \"tex\"\n  ],\n  \"toc\": [\n    \"tex\"\n  ],\n  \"tea\": [\n    \"tea\"\n  ],\n  \"txt\": [\n    \"text\"\n  ],\n  \"textile\": [\n    \"textile\"\n  ],\n  \"thrift\": [\n    \"thrift\"\n  ],\n  \"tu\": [\n    \"turing\"\n  ],\n  \"ttl\": [\n    \"turtle\"\n  ],\n  \"twig\": [\n    \"twig\"\n  ],\n  \"ts\": [\n    \"typescript\",\n    \"xml\"\n  ],\n  \"upc\": [\n    \"unified parallel c\"\n  ],\n  \"uc\": [\n    \"unrealscript\"\n  ],\n  \"vcl\": [\n    \"vcl\"\n  ],\n  \"vhdl\": [\n    \"vhdl\"\n  ],\n  \"vhd\": [\n    \"vhdl\"\n  ],\n  \"vhf\": [\n    \"vhdl\"\n  ],\n  \"vhi\": [\n    \"vhdl\"\n  ],\n  \"vho\": [\n    \"vhdl\"\n  ],\n  \"vhs\": [\n    \"vhdl\"\n  ],\n  \"vht\": [\n    \"vhdl\"\n  ],\n  \"vhw\": [\n    \"vhdl\"\n  ],\n  \"vala\": [\n    \"vala\"\n  ],\n  \"vapi\": [\n    \"vala\"\n  ],\n  \"veo\": [\n    \"verilog\"\n  ],\n  \"vim\": [\n    \"viml\"\n  ],\n  \"vb\": [\n    \"visual basic\"\n  ],\n  \"bas\": [\n    \"visual basic\"\n  ],\n  \"frm\": [\n    \"visual basic\"\n  ],\n  \"frx\": [\n    \"visual basic\"\n  ],\n  \"vba\": [\n    \"visual basic\"\n  ],\n  \"vbhtml\": [\n    \"visual basic\"\n  ],\n  \"vbs\": [\n    \"visual basic\"\n  ],\n  \"volt\": [\n    \"volt\"\n  ],\n  \"owl\": [\n    \"web ontology language\"\n  ],\n  \"webidl\": [\n    \"webidl\"\n  ],\n  \"xc\": [\n    \"xc\"\n  ],\n  \"xml\": [\n    \"xml\"\n  ],\n  \"ant\": [\n    \"xml\"\n  ],\n  \"axml\": [\n    \"xml\"\n  ],\n  \"ccxml\": [\n    \"xml\"\n  ],\n  \"clixml\": [\n    \"xml\"\n  ],\n  \"cproject\": [\n    \"xml\"\n  ],\n  \"csproj\": [\n    \"xml\"\n  ],\n  \"ct\": [\n    \"xml\"\n  ],\n  \"dita\": [\n    \"xml\"\n  ],\n  \"ditamap\": [\n    \"xml\"\n  ],\n  \"ditaval\": [\n    \"xml\"\n  ],\n  \"dll.config\": [\n    \"xml\"\n  ],\n  \"filters\": [\n    \"xml\"\n  ],\n  \"fsproj\": [\n    \"xml\"\n  ],\n  \"fxml\": [\n    \"xml\"\n  ],\n  \"glade\": [\n    \"xml\"\n  ],\n  \"grxml\": [\n    \"xml\"\n  ],\n  \"ivy\": [\n    \"xml\"\n  ],\n  \"jelly\": [\n    \"xml\"\n  ],\n  \"kml\": [\n    \"xml\"\n  ],\n  \"launch\": [\n    \"xml\"\n  ],\n  \"mxml\": [\n    \"xml\"\n  ],\n  \"nproj\": [\n    \"xml\"\n  ],\n  \"nuspec\": [\n    \"xml\"\n  ],\n  \"odd\": [\n    \"xml\"\n  ],\n  \"osm\": [\n    \"xml\"\n  ],\n  \"plist\": [\n    \"xml\"\n  ],\n  \"ps1xml\": [\n    \"xml\"\n  ],\n  \"psc1\": [\n    \"xml\"\n  ],\n  \"pt\": [\n    \"xml\"\n  ],\n  \"rdf\": [\n    \"xml\"\n  ],\n  \"rss\": [\n    \"xml\"\n  ],\n  \"scxml\": [\n    \"xml\"\n  ],\n  \"srdf\": [\n    \"xml\"\n  ],\n  \"storyboard\": [\n    \"xml\"\n  ],\n  \"stTheme\": [\n    \"xml\"\n  ],\n  \"sublime-snippet\": [\n    \"xml\"\n  ],\n  \"targets\": [\n    \"xml\"\n  ],\n  \"tmCommand\": [\n    \"xml\"\n  ],\n  \"tml\": [\n    \"xml\"\n  ],\n  \"tmLanguage\": [\n    \"xml\"\n  ],\n  \"tmPreferences\": [\n    \"xml\"\n  ],\n  \"tmSnippet\": [\n    \"xml\"\n  ],\n  \"tmTheme\": [\n    \"xml\"\n  ],\n  \"ui\": [\n    \"xml\"\n  ],\n  \"urdf\": [\n    \"xml\"\n  ],\n  \"vbproj\": [\n    \"xml\"\n  ],\n  \"vcxproj\": [\n    \"xml\"\n  ],\n  \"vxml\": [\n    \"xml\"\n  ],\n  \"wsdl\": [\n    \"xml\"\n  ],\n  \"wsf\": [\n    \"xml\"\n  ],\n  \"wxi\": [\n    \"xml\"\n  ],\n  \"wxl\": [\n    \"xml\"\n  ],\n  \"wxs\": [\n    \"xml\"\n  ],\n  \"x3d\": [\n    \"xml\"\n  ],\n  \"xacro\": [\n    \"xml\"\n  ],\n  \"xaml\": [\n    \"xml\"\n  ],\n  \"xib\": [\n    \"xml\"\n  ],\n  \"xlf\": [\n    \"xml\"\n  ],\n  \"xliff\": [\n    \"xml\"\n  ],\n  \"xmi\": [\n    \"xml\"\n  ],\n  \"xml.dist\": [\n    \"xml\"\n  ],\n  \"xsd\": [\n    \"xml\"\n  ],\n  \"xul\": [\n    \"xml\"\n  ],\n  \"zcml\": [\n    \"xml\"\n  ],\n  \"xpl\": [\n    \"xproc\"\n  ],\n  \"xproc\": [\n    \"xproc\"\n  ],\n  \"xquery\": [\n    \"xquery\"\n  ],\n  \"xq\": [\n    \"xquery\"\n  ],\n  \"xql\": [\n    \"xquery\"\n  ],\n  \"xqm\": [\n    \"xquery\"\n  ],\n  \"xqy\": [\n    \"xquery\"\n  ],\n  \"xs\": [\n    \"xs\"\n  ],\n  \"xslt\": [\n    \"xslt\"\n  ],\n  \"xsl\": [\n    \"xslt\"\n  ],\n  \"xojo_code\": [\n    \"xojo\"\n  ],\n  \"xojo_menu\": [\n    \"xojo\"\n  ],\n  \"xojo_report\": [\n    \"xojo\"\n  ],\n  \"xojo_script\": [\n    \"xojo\"\n  ],\n  \"xojo_toolbar\": [\n    \"xojo\"\n  ],\n  \"xojo_window\": [\n    \"xojo\"\n  ],\n  \"xtend\": [\n    \"xtend\"\n  ],\n  \"yml\": [\n    \"yaml\"\n  ],\n  \"reek\": [\n    \"yaml\"\n  ],\n  \"rviz\": [\n    \"yaml\"\n  ],\n  \"yaml\": [\n    \"yaml\"\n  ],\n  \"zep\": [\n    \"zephir\"\n  ],\n  \"zimpl\": [\n    \"zimpl\"\n  ],\n  \"zmpl\": [\n    \"zimpl\"\n  ],\n  \"zpl\": [\n    \"zimpl\"\n  ],\n  \"desktop\": [\n    \"desktop\"\n  ],\n  \"desktop.in\": [\n    \"desktop\"\n  ],\n  \"ec\": [\n    \"ec\"\n  ],\n  \"eh\": [\n    \"ec\"\n  ],\n  \"edn\": [\n    \"edn\"\n  ],\n  \"fish\": [\n    \"fish\"\n  ],\n  \"mu\": [\n    \"mupad\"\n  ],\n  \"nc\": [\n    \"nesc\"\n  ],\n  \"ooc\": [\n    \"ooc\"\n  ],\n  \"rst\": [\n    \"restructuredtext\"\n  ],\n  \"rest\": [\n    \"restructuredtext\"\n  ],\n  \"wisp\": [\n    \"wisp\"\n  ],\n  \"prg\": [\n    \"xbase\"\n  ]\n}", "/*!\n * lang-map <https://github.com/jonschlinkert/lang-map>\n *\n * Copyright (c) 2014-2015, <PERSON>.\n * Licensed under the MIT license.\n */\n\n'use strict';\n\n// Lazy-load and cache extensions and languages\nfunction map() {\n  var cache = {};\n  if (!cache.extensions) cache.extensions = require('./lib/exts.json');\n  if (!cache.languages) cache.languages = require('./lib/lang.json');\n  return cache;\n}\n\n/**\n * Get the list of extensions mapped to the given `language`\n *\n * @param  {String} `language`\n * @return {Array}\n */\n\nmap.extensions = function extensions(lang) {\n  lang = normalize(lang);\n  var langs = map().languages;\n  var exts = map().extensions;\n  return exts[lang] || exts[langs[lang]] || [lang];\n};\n\n/**\n * Get the languages mapped to the given `ext`\n *\n * @param  {String} `ext`\n * @return {String}\n */\n\nmap.languages = function languages(ext) {\n  ext = normalize(ext);\n  var langs = map().languages;\n  var exts = map().extensions;\n  return langs[ext] || langs[exts[ext]] || [ext];\n};\n\n/**\n * Normalize the given language or extension\n */\n\nfunction normalize(str) {\n  if (str.charAt(0) === '.') {\n    str = str.slice(1);\n  }\n  return str.toLowerCase();\n}\n\n/**\n * Expose `langMap`\n */\n\nmodule.exports = map;\n"], "mappings": ";;;;;AAAA;AAAA;AAAA;AAAA,MACE,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,cAAc;AAAA,QACZ;AAAA,QACA;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,QACA;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,cAAgB;AAAA,QACd;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAc;AAAA,QACZ;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,aAAe;AAAA,QACb;AAAA,QACA;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,SAAW;AAAA,QACT;AAAA,MACF;AAAA,MACA,UAAY;AAAA,QACV;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,SAAW;AAAA,QACT;AAAA,MACF;AAAA,MACA,UAAY;AAAA,QACV;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,QAAU;AAAA,QACR;AAAA,MACF;AAAA,MACA,YAAc;AAAA,QACZ;AAAA,QACA;AAAA,MACF;AAAA,MACA,QAAU;AAAA,QACR;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,WAAa;AAAA,QACX;AAAA,QACA;AAAA,MACF;AAAA,MACA,SAAW;AAAA,QACT;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,SAAW;AAAA,QACT;AAAA,MACF;AAAA,MACA,YAAc;AAAA,QACZ;AAAA,QACA;AAAA,MACF;AAAA,MACA,UAAY;AAAA,QACV;AAAA,MACF;AAAA,MACA,UAAY;AAAA,QACV;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,WAAa;AAAA,QACX;AAAA,QACA;AAAA,MACF;AAAA,MACA,cAAgB;AAAA,QACd;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,GAAK;AAAA,QACH;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,MAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,OAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,aAAa;AAAA,QACX;AAAA,MACF;AAAA,MACA,gBAAgB;AAAA,QACd;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,eAAe;AAAA,QACb;AAAA,MACF;AAAA,MACA,UAAY;AAAA,QACV;AAAA,MACF;AAAA,MACA,QAAU;AAAA,QACR;AAAA,MACF;AAAA,MACA,QAAU;AAAA,QACR;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,SAAW;AAAA,QACT;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,SAAW;AAAA,QACT;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,cAAgB;AAAA,QACd;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAc;AAAA,QACZ;AAAA,QACA;AAAA,MACF;AAAA,MACA,kBAAkB;AAAA,QAChB;AAAA,MACF;AAAA,MACA,eAAe;AAAA,QACb;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,oBAAoB;AAAA,QAClB;AAAA,QACA;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,QACA;AAAA,MACF;AAAA,MACA,eAAe;AAAA,QACb;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,QAAU;AAAA,QACR;AAAA,MACF;AAAA,MACA,SAAW;AAAA,QACT;AAAA,MACF;AAAA,MACA,UAAY;AAAA,QACV;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAAA,MACA,SAAW;AAAA,QACT;AAAA,MACF;AAAA,MACA,QAAU;AAAA,QACR;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,GAAK;AAAA,QACH;AAAA,QACA;AAAA,MACF;AAAA,MACA,aAAa;AAAA,QACX;AAAA,MACF;AAAA,MACA,4BAA4B;AAAA,QAC1B;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,MACF;AAAA,MACA,QAAU;AAAA,QACR;AAAA,MACF;AAAA,MACA,eAAe;AAAA,QACb;AAAA,QACA;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAc;AAAA,QACZ;AAAA,MACF;AAAA,MACA,YAAc;AAAA,QACZ;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,GAAK;AAAA,QACH;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,QACA;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,kBAAkB;AAAA,QAChB;AAAA,MACF;AAAA,MACA,QAAU;AAAA,QACR;AAAA,MACF;AAAA,MACA,QAAU;AAAA,QACR;AAAA,QACA;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,cAAc;AAAA,QACZ;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,aAAe;AAAA,QACb;AAAA,QACA;AAAA,MACF;AAAA,MACA,QAAU;AAAA,QACR;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,MAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAAA,MACA,SAAW;AAAA,QACT;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,QAAU;AAAA,QACR;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,QAAU;AAAA,QACR;AAAA,MACF;AAAA,MACA,cAAgB;AAAA,QACd;AAAA,MACF;AAAA,MACA,WAAa;AAAA,QACX;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,UAAU;AAAA,QACR;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,UAAY;AAAA,QACV;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,uBAAuB;AAAA,QACrB;AAAA,MACF;AAAA,MACA,QAAU;AAAA,QACR;AAAA,MACF;AAAA,MACA,iBAAiB;AAAA,QACf;AAAA,MACF;AAAA,MACA,iBAAiB;AAAA,QACf;AAAA,MACF;AAAA,MACA,mBAAmB;AAAA,QACjB;AAAA,QACA;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,SAAW;AAAA,QACT;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,QAAU;AAAA,QACR;AAAA,MACF;AAAA,MACA,yBAAyB;AAAA,QACvB;AAAA,MACF;AAAA,MACA,2BAA2B;AAAA,QACzB;AAAA,MACF;AAAA,MACA,kBAAkB;AAAA,QAChB;AAAA,QACA;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,QAAU;AAAA,QACR;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,uBAAuB;AAAA,QACrB;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,eAAe;AAAA,QACb;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAc;AAAA,QACZ;AAAA,QACA;AAAA,MACF;AAAA,MACA,SAAW;AAAA,QACT;AAAA,MACF;AAAA,MACA,SAAW;AAAA,QACT;AAAA,QACA;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,WAAW;AAAA,QACT;AAAA,QACA;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV;AAAA,QACA;AAAA,MACF;AAAA,MACA,cAAc;AAAA,QACZ;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,UAAY;AAAA,QACV;AAAA,MACF;AAAA,MACA,GAAK;AAAA,QACH;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,QAAU;AAAA,QACR;AAAA,MACF;AAAA,MACA,QAAU;AAAA,QACR;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,QAAU;AAAA,QACR;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,qBAAqB;AAAA,QACnB;AAAA,MACF;AAAA,MACA,YAAc;AAAA,QACZ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,QAAU;AAAA,QACR;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,SAAW;AAAA,QACT;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,SAAW;AAAA,QACT;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,UAAY;AAAA,QACV;AAAA,QACA;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,iBAAiB;AAAA,QACf;AAAA,QACA;AAAA,MACF;AAAA,MACA,QAAU;AAAA,QACR;AAAA,MACF;AAAA,MACA,iBAAiB;AAAA,QACf;AAAA,MACF;AAAA,MACA,yBAAyB;AAAA,QACvB;AAAA,MACF;AAAA,MACA,oBAAoB;AAAA,QAClB;AAAA,MACF;AAAA,MACA,YAAc;AAAA,QACZ;AAAA,QACA;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,SAAW;AAAA,QACT;AAAA,QACA;AAAA,MACF;AAAA,MACA,QAAU;AAAA,QACR;AAAA,MACF;AAAA,MACA,YAAc;AAAA,QACZ;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,GAAK;AAAA,QACH;AAAA,QACA;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,QACA;AAAA,MACF;AAAA,MACA,UAAY;AAAA,QACV;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAAA,MACA,UAAY;AAAA,QACV;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,aAAe;AAAA,QACb;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,QAAU;AAAA,QACR;AAAA,QACA;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,WAAa;AAAA,QACX;AAAA,MACF;AAAA,MACA,SAAW;AAAA,QACT;AAAA,QACA;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,UAAY;AAAA,QACV;AAAA,MACF;AAAA,MACA,4BAA4B;AAAA,QAC1B;AAAA,QACA;AAAA,MACF;AAAA,MACA,QAAU;AAAA,QACR;AAAA,MACF;AAAA,MACA,SAAW;AAAA,QACT;AAAA,MACF;AAAA,MACA,YAAc;AAAA,QACZ;AAAA,MACF;AAAA,MACA,QAAU;AAAA,QACR;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAAA,MACA,SAAW;AAAA,QACT;AAAA,MACF;AAAA,MACA,SAAW;AAAA,QACT;AAAA,QACA;AAAA,MACF;AAAA,MACA,eAAe;AAAA,QACb;AAAA,QACA;AAAA,MACF;AAAA,MACA,SAAW;AAAA,QACT;AAAA,MACF;AAAA,MACA,SAAW;AAAA,QACT;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,QAAU;AAAA,QACR;AAAA,QACA;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,SAAW;AAAA,QACT;AAAA,MACF;AAAA,MACA,eAAe;AAAA,QACb;AAAA,QACA;AAAA,MACF;AAAA,MACA,iBAAiB;AAAA,QACf;AAAA,MACF;AAAA,MACA,eAAe;AAAA,QACb;AAAA,QACA;AAAA,MACF;AAAA,MACA,SAAW;AAAA,QACT;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,QAAU;AAAA,QACR;AAAA,QACA;AAAA,MACF;AAAA,MACA,gBAAgB;AAAA,QACd;AAAA,QACA;AAAA,MACF;AAAA,MACA,UAAY;AAAA,QACV;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,SAAW;AAAA,QACT;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,SAAW;AAAA,QACT;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,SAAW;AAAA,QACT;AAAA,MACF;AAAA,MACA,QAAU;AAAA,QACR;AAAA,MACF;AAAA,MACA,mBAAmB;AAAA,QACjB;AAAA,MACF;AAAA,MACA,kCAAkC;AAAA,QAChC;AAAA,MACF;AAAA,MACA,QAAU;AAAA,QACR;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,UAAY;AAAA,QACV;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,YAAc;AAAA,QACZ;AAAA,MACF;AAAA,MACA,YAAc;AAAA,QACZ;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAc;AAAA,QACZ;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAc;AAAA,QACZ;AAAA,MACF;AAAA,MACA,QAAU;AAAA,QACR;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,kBAAkB;AAAA,QAChB;AAAA,MACF;AAAA,MACA,mBAAmB;AAAA,QACjB;AAAA,MACF;AAAA,MACA,cAAc;AAAA,QACZ;AAAA,QACA;AAAA,MACF;AAAA,MACA,QAAU;AAAA,QACR;AAAA,MACF;AAAA,MACA,aAAa;AAAA,QACX;AAAA,MACF;AAAA,MACA,WAAa;AAAA,QACX;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAc;AAAA,QACZ;AAAA,MACF;AAAA,MACA,QAAU;AAAA,QACR;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,oBAAoB;AAAA,QAClB;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,GAAK;AAAA,QACH;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,WAAa;AAAA,QACX;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,WAAa;AAAA,QACX;AAAA,MACF;AAAA,MACA,QAAU;AAAA,QACR;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,sBAAsB;AAAA,QACpB;AAAA,MACF;AAAA,MACA,kBAAkB;AAAA,QAChB;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,QACA;AAAA,MACF;AAAA,MACA,SAAW;AAAA,QACT;AAAA,MACF;AAAA,MACA,cAAgB;AAAA,QACd;AAAA,QACA;AAAA,MACF;AAAA,MACA,gBAAkB;AAAA,QAChB;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,QAAU;AAAA,QACR;AAAA,QACA;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,QACA;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAAA,MACA,WAAa;AAAA,QACX;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,QAAU;AAAA,QACR;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,QAAU;AAAA,QACR;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,cAAgB;AAAA,QACd;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,WAAa;AAAA,QACX;AAAA,QACA;AAAA,MACF;AAAA,MACA,QAAU;AAAA,QACR;AAAA,MACF;AAAA,MACA,YAAc;AAAA,QACZ;AAAA,QACA;AAAA,MACF;AAAA,MACA,UAAY;AAAA,QACV;AAAA,MACF;AAAA,MACA,eAAe;AAAA,QACb;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,QAAU;AAAA,QACR;AAAA,MACF;AAAA,MACA,eAAiB;AAAA,QACf;AAAA,QACA;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,eAAiB;AAAA,QACf;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAAA,MACA,SAAW;AAAA,QACT;AAAA,MACF;AAAA,MACA,QAAU;AAAA,QACR;AAAA,MACF;AAAA,MACA,QAAU;AAAA,QACR;AAAA,QACA;AAAA,MACF;AAAA,MACA,QAAU;AAAA,QACR;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,YAAc;AAAA,QACZ;AAAA,MACF;AAAA,MACA,sBAAsB;AAAA,QACpB;AAAA,MACF;AAAA,MACA,cAAgB;AAAA,QACd;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAAA,MACA,SAAW;AAAA,QACT;AAAA,QACA;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,gBAAgB;AAAA,QACd;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,yBAAyB;AAAA,QACvB;AAAA,MACF;AAAA,MACA,QAAU;AAAA,QACR;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,QAAU;AAAA,QACR;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,QAAU;AAAA,QACR;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,SAAW;AAAA,QACT;AAAA,QACA;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,kBAAoB;AAAA,QAClB;AAAA,QACA;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,MACF;AAAA,IACF;AAAA;AAAA;;;AC7iDA;AAAA;AAAA;AAAA,MACE,KAAK;AAAA,QACH;AAAA,MACF;AAAA,MACA,KAAK;AAAA,QACH;AAAA,MACF;AAAA,MACA,KAAK;AAAA,QACH;AAAA,MACF;AAAA,MACA,KAAK;AAAA,QACH;AAAA,MACF;AAAA,MACA,KAAK;AAAA,QACH;AAAA,MACF;AAAA,MACA,KAAK;AAAA,QACH;AAAA,MACF;AAAA,MACA,KAAK;AAAA,QACH;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,QAAU;AAAA,QACR;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,YAAc;AAAA,QACZ;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,aAAe;AAAA,QACb;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,UAAY;AAAA,QACV;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,SAAW;AAAA,QACT;AAAA,MACF;AAAA,MACA,GAAK;AAAA,QACH;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,GAAK;AAAA,QACH;AAAA,QACA;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,GAAK;AAAA,QACH;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,GAAK;AAAA,QACH;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,GAAK;AAAA,QACH;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF;AAAA,MACA,QAAU;AAAA,QACR;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,OAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,OAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,aAAa;AAAA,QACX;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,QAAU;AAAA,QACR;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,WAAW;AAAA,QACT;AAAA,MACF;AAAA,MACA,QAAU;AAAA,QACR;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,QAAU;AAAA,QACR;AAAA,MACF;AAAA,MACA,SAAW;AAAA,QACT;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,QACA;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,GAAK;AAAA,QACH;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAc;AAAA,QACZ;AAAA,MACF;AAAA,MACA,eAAe;AAAA,QACb;AAAA,MACF;AAAA,MACA,cAAc;AAAA,QACZ;AAAA,MACF;AAAA,MACA,eAAe;AAAA,QACb;AAAA,MACF;AAAA,MACA,eAAe;AAAA,QACb;AAAA,MACF;AAAA,MACA,QAAU;AAAA,QACR;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,MACF;AAAA,MACA,SAAW;AAAA,QACT;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,GAAK;AAAA,QACH;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,MACF;AAAA,MACA,aAAa;AAAA,QACX;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,MACF;AAAA,MACA,YAAc;AAAA,QACZ;AAAA,MACF;AAAA,MACA,QAAU;AAAA,QACR;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAc;AAAA,QACZ;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,GAAK;AAAA,QACH;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,QACA;AAAA,MACF;AAAA,MACA,QAAU;AAAA,QACR;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,QACA;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,GAAK;AAAA,QACH;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,iBAAiB;AAAA,QACf;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,MACF;AAAA,MACA,aAAe;AAAA,QACb;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,MACF;AAAA,MACA,SAAW;AAAA,QACT;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,GAAK;AAAA,QACH;AAAA,QACA;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,QAAU;AAAA,QACR;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,MACF;AAAA,MACA,WAAa;AAAA,QACX;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,OAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,GAAK;AAAA,QACH;AAAA,QACA;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,QACA;AAAA,MACF;AAAA,MACA,GAAK;AAAA,QACH;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,SAAW;AAAA,QACT;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,SAAW;AAAA,QACT;AAAA,MACF;AAAA,MACA,QAAU;AAAA,QACR;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,SAAW;AAAA,QACT;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,QACA;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,QAAU;AAAA,QACR;AAAA,MACF;AAAA,MACA,QAAU;AAAA,QACR;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,SAAW;AAAA,QACT;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,QAAU;AAAA,QACR;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,QAAU;AAAA,QACR;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,WAAW;AAAA,QACT;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,UAAY;AAAA,QACV;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,cAAc;AAAA,QACZ;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,QACA;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,eAAe;AAAA,QACb;AAAA,MACF;AAAA,MACA,YAAc;AAAA,QACZ;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAc;AAAA,QACZ;AAAA,MACF;AAAA,MACA,QAAU;AAAA,QACR;AAAA,MACF;AAAA,MACA,YAAc;AAAA,QACZ;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,QAAU;AAAA,QACR;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,GAAK;AAAA,QACH;AAAA,QACA;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,iBAAiB;AAAA,QACf;AAAA,MACF;AAAA,MACA,oBAAoB;AAAA,QAClB;AAAA,MACF;AAAA,MACA,uBAAuB;AAAA,QACrB;AAAA,MACF;AAAA,MACA,kBAAkB;AAAA,QAChB;AAAA,MACF;AAAA,MACA,iBAAiB;AAAA,QACf;AAAA,MACF;AAAA,MACA,gBAAgB;AAAA,QACd;AAAA,MACF;AAAA,MACA,oBAAoB;AAAA,QAClB;AAAA,MACF;AAAA,MACA,mBAAmB;AAAA,QACjB;AAAA,MACF;AAAA,MACA,oBAAoB;AAAA,QAClB;AAAA,MACF;AAAA,MACA,iBAAiB;AAAA,QACf;AAAA,MACF;AAAA,MACA,qBAAqB;AAAA,QACnB;AAAA,MACF;AAAA,MACA,iBAAmB;AAAA,QACjB;AAAA,MACF;AAAA,MACA,iBAAmB;AAAA,QACjB;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,SAAW;AAAA,QACT;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,QAAU;AAAA,QACR;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,QAAU;AAAA,QACR;AAAA,MACF;AAAA,MACA,QAAU;AAAA,QACR;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,GAAK;AAAA,QACH;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,QAAU;AAAA,QACR;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,WAAa;AAAA,QACX;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,MACF;AAAA,MACA,GAAK;AAAA,QACH;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,SAAW;AAAA,QACT;AAAA,MACF;AAAA,MACA,QAAU;AAAA,QACR;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,QAAU;AAAA,QACR;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,MACF;AAAA,MACA,UAAY;AAAA,QACV;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,QAAU;AAAA,QACR;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,aAAe;AAAA,QACb;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,QAAU;AAAA,QACR;AAAA,MACF;AAAA,MACA,QAAU;AAAA,QACR;AAAA,MACF;AAAA,MACA,SAAW;AAAA,QACT;AAAA,MACF;AAAA,MACA,SAAW;AAAA,QACT;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,WAAa;AAAA,QACX;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,QACA;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,QAAU;AAAA,QACR;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,GAAK;AAAA,QACH;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,WAAW;AAAA,QACT;AAAA,MACF;AAAA,MACA,WAAW;AAAA,QACT;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,WAAa;AAAA,QACX;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,QAAU;AAAA,QACR;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,QAAU;AAAA,QACR;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,QAAU;AAAA,QACR;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,SAAW;AAAA,QACT;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,MACF;AAAA,MACA,SAAW;AAAA,QACT;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,QAAU;AAAA,QACR;AAAA,MACF;AAAA,MACA,GAAK;AAAA,QACH;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,SAAW;AAAA,QACT;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,QAAU;AAAA,QACR;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,QACA;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,GAAK;AAAA,QACH;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,OAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,OAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,QAAU;AAAA,QACR;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,GAAK;AAAA,QACH;AAAA,QACA;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,QAAU;AAAA,QACR;AAAA,MACF;AAAA,MACA,WAAa;AAAA,QACX;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,MACF;AAAA,MACA,SAAW;AAAA,QACT;AAAA,MACF;AAAA,MACA,SAAW;AAAA,QACT;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,UAAY;AAAA,QACV;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAc;AAAA,QACZ;AAAA,QACA;AAAA,MACF;AAAA,MACA,SAAW;AAAA,QACT;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,QAAU;AAAA,QACR;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,QAAU;AAAA,QACR;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,QAAU;AAAA,QACR;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,QAAU;AAAA,QACR;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,QACA;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,SAAW;AAAA,QACT;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,cAAc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cAAc;AAAA,QACZ;AAAA,MACF;AAAA,MACA,SAAW;AAAA,QACT;AAAA,MACF;AAAA,MACA,QAAU;AAAA,QACR;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,QAAU;AAAA,QACR;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,QAAU;AAAA,QACR;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,QAAU;AAAA,QACR;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,YAAc;AAAA,QACZ;AAAA,MACF;AAAA,MACA,SAAW;AAAA,QACT;AAAA,MACF;AAAA,MACA,mBAAmB;AAAA,QACjB;AAAA,MACF;AAAA,MACA,SAAW;AAAA,QACT;AAAA,MACF;AAAA,MACA,WAAa;AAAA,QACX;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,YAAc;AAAA,QACZ;AAAA,MACF;AAAA,MACA,eAAiB;AAAA,QACf;AAAA,MACF;AAAA,MACA,WAAa;AAAA,QACX;AAAA,MACF;AAAA,MACA,SAAW;AAAA,QACT;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,QAAU;AAAA,QACR;AAAA,MACF;AAAA,MACA,SAAW;AAAA,QACT;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,QAAU;AAAA,QACR;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,WAAa;AAAA,QACX;AAAA,MACF;AAAA,MACA,WAAa;AAAA,QACX;AAAA,MACF;AAAA,MACA,aAAe;AAAA,QACb;AAAA,MACF;AAAA,MACA,aAAe;AAAA,QACb;AAAA,MACF;AAAA,MACA,cAAgB;AAAA,QACd;AAAA,MACF;AAAA,MACA,aAAe;AAAA,QACb;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,OAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,SAAW;AAAA,QACT;AAAA,MACF;AAAA,MACA,cAAc;AAAA,QACZ;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,MACF;AAAA,MACA,IAAM;AAAA,QACJ;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,MAAQ;AAAA,QACN;AAAA,MACF;AAAA,MACA,KAAO;AAAA,QACL;AAAA,MACF;AAAA,IACF;AAAA;AAAA;;;ACr6EA;AAAA;AAUA,aAAS,MAAM;AACb,UAAI,QAAQ,CAAC;AACb,UAAI,CAAC,MAAM,WAAY,OAAM,aAAa;AAC1C,UAAI,CAAC,MAAM,UAAW,OAAM,YAAY;AACxC,aAAO;AAAA,IACT;AASA,QAAI,aAAa,SAAS,WAAW,MAAM;AACzC,aAAO,UAAU,IAAI;AACrB,UAAI,QAAQ,IAAI,EAAE;AAClB,UAAI,OAAO,IAAI,EAAE;AACjB,aAAO,KAAK,IAAI,KAAK,KAAK,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI;AAAA,IACjD;AASA,QAAI,YAAY,SAAS,UAAU,KAAK;AACtC,YAAM,UAAU,GAAG;AACnB,UAAI,QAAQ,IAAI,EAAE;AAClB,UAAI,OAAO,IAAI,EAAE;AACjB,aAAO,MAAM,GAAG,KAAK,MAAM,KAAK,GAAG,CAAC,KAAK,CAAC,GAAG;AAAA,IAC/C;AAMA,aAAS,UAAU,KAAK;AACtB,UAAI,IAAI,OAAO,CAAC,MAAM,KAAK;AACzB,cAAM,IAAI,MAAM,CAAC;AAAA,MACnB;AACA,aAAO,IAAI,YAAY;AAAA,IACzB;AAMA,WAAO,UAAU;AAAA;AAAA;", "names": []}