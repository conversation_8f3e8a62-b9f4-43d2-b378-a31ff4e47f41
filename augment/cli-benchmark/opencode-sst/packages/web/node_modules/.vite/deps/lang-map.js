import {
  __commonJS
} from "./chunk-5AQFBOJN.js";

// ../../node_modules/lang-map/lib/exts.json
var require_exts = __commonJS({
  "../../node_modules/lang-map/lib/exts.json"(exports, module) {
    module.exports = {
      abap: [
        "abap"
      ],
      "ags script": [
        "asc",
        "ash"
      ],
      ampl: [
        "ampl"
      ],
      antlr: [
        "g4"
      ],
      apl: [
        "apl",
        "dyalog"
      ],
      asp: [
        "asp",
        "asax",
        "ascx",
        "ashx",
        "asmx",
        "aspx",
        "axd"
      ],
      ats: [
        "dats",
        "hats",
        "sats"
      ],
      actionscript: [
        "as"
      ],
      ada: [
        "adb",
        "ada",
        "ads"
      ],
      agda: [
        "agda"
      ],
      alloy: [
        "als"
      ],
      apacheconf: [
        "apacheconf"
      ],
      apex: [
        "cls"
      ],
      applescript: [
        "applescript",
        "scpt"
      ],
      arc: [
        "arc"
      ],
      arduino: [
        "ino"
      ],
      asciidoc: [
        "asciidoc",
        "adoc",
        "asc"
      ],
      aspectj: [
        "aj"
      ],
      assembly: [
        "asm",
        "a51",
        "nasm"
      ],
      augeas: [
        "aug"
      ],
      autohotkey: [
        "ahk",
        "ahkl"
      ],
      autoit: [
        "au3"
      ],
      awk: [
        "awk",
        "auk",
        "gawk",
        "mawk",
        "nawk"
      ],
      batchfile: [
        "bat",
        "cmd"
      ],
      befunge: [
        "befunge"
      ],
      bison: [
        "y"
      ],
      bitbake: [
        "bb"
      ],
      blitzbasic: [
        "bb",
        "decls"
      ],
      blitzmax: [
        "bmx"
      ],
      bluespec: [
        "bsv"
      ],
      boo: [
        "boo"
      ],
      brainfuck: [
        "b",
        "bf"
      ],
      brightscript: [
        "brs"
      ],
      bro: [
        "bro"
      ],
      c: [
        "c",
        "cats",
        "h",
        "idc",
        "w"
      ],
      "c#": [
        "cs",
        "cshtml",
        "csx"
      ],
      "c++": [
        "cpp",
        "c++",
        "cc",
        "cp",
        "cxx",
        "h",
        "h++",
        "hh",
        "hpp",
        "hxx",
        "inl",
        "ipp",
        "tcc",
        "tpp"
      ],
      "c-objdump": [
        "c-objdump"
      ],
      "c2hs haskell": [
        "chs"
      ],
      clips: [
        "clp"
      ],
      cmake: [
        "cmake",
        "cmake.in"
      ],
      cobol: [
        "cob",
        "cbl",
        "ccp",
        "cobol",
        "cpy"
      ],
      css: [
        "css"
      ],
      "cap'n proto": [
        "capnp"
      ],
      cartocss: [
        "mss"
      ],
      ceylon: [
        "ceylon"
      ],
      chapel: [
        "chpl"
      ],
      chuck: [
        "ck"
      ],
      cirru: [
        "cirru"
      ],
      clarion: [
        "clw"
      ],
      clean: [
        "icl",
        "dcl"
      ],
      clojure: [
        "clj",
        "boot",
        "cl2",
        "cljc",
        "cljs",
        "cljs.hl",
        "cljscm",
        "cljx",
        "hic"
      ],
      coffeescript: [
        "coffee",
        "_coffee",
        "cjsx",
        "cson",
        "iced"
      ],
      coldfusion: [
        "cfm",
        "cfml"
      ],
      "coldfusion cfc": [
        "cfc"
      ],
      "common lisp": [
        "lisp",
        "asd",
        "cl",
        "lsp",
        "ny",
        "podsl"
      ],
      "component pascal": [
        "cp",
        "cps"
      ],
      cool: [
        "cl"
      ],
      coq: [
        "coq",
        "v"
      ],
      "cpp-objdump": [
        "cppobjdump",
        "c++-objdump",
        "c++objdump",
        "cpp-objdump",
        "cxx-objdump"
      ],
      creole: [
        "creole"
      ],
      crystal: [
        "cr"
      ],
      cucumber: [
        "feature"
      ],
      cuda: [
        "cu",
        "cuh"
      ],
      cycript: [
        "cy"
      ],
      cython: [
        "pyx",
        "pxd",
        "pxi"
      ],
      d: [
        "d",
        "di"
      ],
      "d-objdump": [
        "d-objdump"
      ],
      "digital command language": [
        "com"
      ],
      dm: [
        "dm"
      ],
      dtrace: [
        "d"
      ],
      "darcs patch": [
        "darcspatch",
        "dpatch"
      ],
      dart: [
        "dart"
      ],
      diff: [
        "diff",
        "patch"
      ],
      dockerfile: [
        "dockerfile"
      ],
      dogescript: [
        "djs"
      ],
      dylan: [
        "dylan",
        "dyl",
        "intr",
        "lid"
      ],
      e: [
        "E"
      ],
      ecl: [
        "ecl",
        "eclxml"
      ],
      eagle: [
        "sch",
        "brd"
      ],
      "ecere projects": [
        "epj"
      ],
      eiffel: [
        "e"
      ],
      elixir: [
        "ex",
        "exs"
      ],
      elm: [
        "elm"
      ],
      "emacs lisp": [
        "el",
        "emacs",
        "emacs.desktop"
      ],
      emberscript: [
        "em",
        "emberscript"
      ],
      erlang: [
        "erl",
        "es",
        "escript",
        "hrl"
      ],
      "f#": [
        "fs",
        "fsi",
        "fsx"
      ],
      flux: [
        "fx",
        "flux"
      ],
      fortran: [
        "f90",
        "f",
        "f03",
        "f08",
        "f77",
        "f95",
        "for",
        "fpp"
      ],
      factor: [
        "factor"
      ],
      fancy: [
        "fy",
        "fancypack"
      ],
      fantom: [
        "fan"
      ],
      filterscript: [
        "fs"
      ],
      formatted: [
        "for"
      ],
      forth: [
        "fth",
        "4th",
        "f",
        "for",
        "forth",
        "fr",
        "frt",
        "fs"
      ],
      frege: [
        "fr"
      ],
      "g-code": [
        "g",
        "gco",
        "gcode"
      ],
      gams: [
        "gms"
      ],
      gap: [
        "g",
        "gap",
        "gd",
        "gi",
        "tst"
      ],
      gas: [
        "s"
      ],
      gdscript: [
        "gd"
      ],
      glsl: [
        "glsl",
        "fp",
        "frag",
        "frg",
        "fs",
        "fshader",
        "geo",
        "geom",
        "glslv",
        "gshader",
        "shader",
        "vert",
        "vrx",
        "vshader"
      ],
      "game maker language": [
        "gml"
      ],
      genshi: [
        "kid"
      ],
      "gentoo ebuild": [
        "ebuild"
      ],
      "gentoo eclass": [
        "eclass"
      ],
      "gettext catalog": [
        "po",
        "pot"
      ],
      glyph: [
        "glf"
      ],
      gnuplot: [
        "gp",
        "gnu",
        "gnuplot",
        "plot",
        "plt"
      ],
      go: [
        "go"
      ],
      golo: [
        "golo"
      ],
      gosu: [
        "gs",
        "gst",
        "gsx",
        "vark"
      ],
      grace: [
        "grace"
      ],
      gradle: [
        "gradle"
      ],
      "grammatical framework": [
        "gf"
      ],
      "graph modeling language": [
        "gml"
      ],
      "graphviz (dot)": [
        "dot",
        "gv"
      ],
      groff: [
        "man",
        "1",
        "2",
        "3",
        "4",
        "5",
        "6",
        "7"
      ],
      groovy: [
        "groovy",
        "grt",
        "gtpl",
        "gvy"
      ],
      "groovy server pages": [
        "gsp"
      ],
      html: [
        "html",
        "htm",
        "html.hl",
        "st",
        "xht",
        "xhtml"
      ],
      "html+django": [
        "mustache",
        "jinja"
      ],
      "html+erb": [
        "erb",
        "erb.deface"
      ],
      "html+php": [
        "phtml"
      ],
      http: [
        "http"
      ],
      hack: [
        "hh",
        "php"
      ],
      haml: [
        "haml",
        "haml.deface"
      ],
      handlebars: [
        "handlebars",
        "hbs"
      ],
      harbour: [
        "hb"
      ],
      haskell: [
        "hs",
        "hsc"
      ],
      haxe: [
        "hx",
        "hxsl"
      ],
      hy: [
        "hy"
      ],
      idl: [
        "pro",
        "dlm"
      ],
      "igor pro": [
        "ipf"
      ],
      ini: [
        "ini",
        "cfg",
        "prefs",
        "pro",
        "properties"
      ],
      "irc log": [
        "irclog",
        "weechatlog"
      ],
      idris: [
        "idr",
        "lidr"
      ],
      "inform 7": [
        "ni",
        "i7x"
      ],
      "inno setup": [
        "iss"
      ],
      io: [
        "io"
      ],
      ioke: [
        "ik"
      ],
      isabelle: [
        "thy"
      ],
      j: [
        "ijs"
      ],
      jflex: [
        "flex",
        "jflex"
      ],
      json: [
        "json",
        "lock"
      ],
      json5: [
        "json5"
      ],
      jsonld: [
        "jsonld"
      ],
      jsoniq: [
        "jq"
      ],
      jade: [
        "jade"
      ],
      jasmin: [
        "j"
      ],
      java: [
        "java"
      ],
      "java server pages": [
        "jsp"
      ],
      javascript: [
        "js",
        "_js",
        "bones",
        "es6",
        "frag",
        "gs",
        "jake",
        "jsb",
        "jsfl",
        "jsm",
        "jss",
        "jsx",
        "njs",
        "pac",
        "sjs",
        "ssjs",
        "sublime-build",
        "sublime-commands",
        "sublime-completions",
        "sublime-keymap",
        "sublime-macro",
        "sublime-menu",
        "sublime-mousemap",
        "sublime-project",
        "sublime-settings",
        "sublime-theme",
        "sublime-workspace",
        "sublime_metrics",
        "sublime_session",
        "xsjs",
        "xsjslib"
      ],
      julia: [
        "jl"
      ],
      krl: [
        "krl"
      ],
      kicad: [
        "sch"
      ],
      kit: [
        "kit"
      ],
      kotlin: [
        "kt",
        "ktm",
        "kts"
      ],
      lfe: [
        "lfe"
      ],
      llvm: [
        "ll"
      ],
      lolcode: [
        "lol"
      ],
      lsl: [
        "lsl"
      ],
      labview: [
        "lvproj"
      ],
      lasso: [
        "lasso",
        "las",
        "lasso8",
        "lasso9",
        "ldml"
      ],
      latte: [
        "latte"
      ],
      lean: [
        "lean",
        "hlean"
      ],
      less: [
        "less"
      ],
      lilypond: [
        "ly",
        "ily"
      ],
      limbo: [
        "b",
        "m"
      ],
      "linker script": [
        "ld",
        "lds"
      ],
      liquid: [
        "liquid"
      ],
      "literate agda": [
        "lagda"
      ],
      "literate coffeescript": [
        "litcoffee"
      ],
      "literate haskell": [
        "lhs"
      ],
      livescript: [
        "ls",
        "_ls"
      ],
      logos: [
        "xm",
        "x",
        "xi"
      ],
      logtalk: [
        "lgt",
        "logtalk"
      ],
      lookml: [
        "lookml"
      ],
      loomscript: [
        "ls"
      ],
      lua: [
        "lua",
        "fcgi",
        "nse",
        "pd_lua",
        "rbxs",
        "wlua"
      ],
      m: [
        "mumps",
        "m"
      ],
      mtml: [
        "mtml"
      ],
      muf: [
        "muf",
        "m"
      ],
      makefile: [
        "mak",
        "d",
        "mk"
      ],
      mako: [
        "mako",
        "mao"
      ],
      markdown: [
        "md",
        "markdown",
        "mkd",
        "mkdn",
        "mkdown",
        "ron"
      ],
      mask: [
        "mask"
      ],
      mathematica: [
        "mathematica",
        "cdf",
        "m",
        "ma",
        "nb",
        "nbp",
        "wl",
        "wlt"
      ],
      matlab: [
        "matlab",
        "m"
      ],
      max: [
        "maxpat",
        "maxhelp",
        "maxproj",
        "mxt",
        "pat"
      ],
      mediawiki: [
        "mediawiki"
      ],
      mercury: [
        "m",
        "moo"
      ],
      minid: [
        "minid"
      ],
      mirah: [
        "druby",
        "duby",
        "mir",
        "mirah"
      ],
      modelica: [
        "mo"
      ],
      "module management system": [
        "mms",
        "mmk"
      ],
      monkey: [
        "monkey"
      ],
      moocode: [
        "moo"
      ],
      moonscript: [
        "moon"
      ],
      myghty: [
        "myt"
      ],
      nl: [
        "nl"
      ],
      nsis: [
        "nsi",
        "nsh"
      ],
      nemerle: [
        "n"
      ],
      netlinx: [
        "axs",
        "axi"
      ],
      "netlinx+erb": [
        "axs.erb",
        "axi.erb"
      ],
      netlogo: [
        "nlogo"
      ],
      newlisp: [
        "nl",
        "lisp",
        "lsp"
      ],
      nginx: [
        "nginxconf"
      ],
      nimrod: [
        "nim",
        "nimrod"
      ],
      ninja: [
        "ninja"
      ],
      nit: [
        "nit"
      ],
      nix: [
        "nix"
      ],
      nu: [
        "nu"
      ],
      numpy: [
        "numpy",
        "numpyw",
        "numsc"
      ],
      ocaml: [
        "ml",
        "eliom",
        "eliomi",
        "ml4",
        "mli",
        "mll",
        "mly"
      ],
      objdump: [
        "objdump"
      ],
      "objective-c": [
        "m",
        "h"
      ],
      "objective-c++": [
        "mm"
      ],
      "objective-j": [
        "j",
        "sj"
      ],
      omgrofl: [
        "omgrofl"
      ],
      opa: [
        "opa"
      ],
      opal: [
        "opal"
      ],
      opencl: [
        "cl",
        "opencl"
      ],
      "openedge abl": [
        "p",
        "cls"
      ],
      openscad: [
        "scad"
      ],
      org: [
        "org"
      ],
      ox: [
        "ox",
        "oxh",
        "oxo"
      ],
      oxygene: [
        "oxygene"
      ],
      oz: [
        "oz"
      ],
      pawn: [
        "pwn"
      ],
      php: [
        "php",
        "aw",
        "ctp",
        "fcgi",
        "php3",
        "php4",
        "php5",
        "phpt"
      ],
      plsql: [
        "pls",
        "pkb",
        "pks",
        "plb",
        "plsql",
        "sql"
      ],
      plpgsql: [
        "sql"
      ],
      pan: [
        "pan"
      ],
      papyrus: [
        "psc"
      ],
      parrot: [
        "parrot"
      ],
      "parrot assembly": [
        "pasm"
      ],
      "parrot internal representation": [
        "pir"
      ],
      pascal: [
        "pas",
        "dfm",
        "dpr",
        "lpr",
        "pp"
      ],
      perl: [
        "pl",
        "cgi",
        "fcgi",
        "perl",
        "ph",
        "plx",
        "pm",
        "pod",
        "psgi",
        "t"
      ],
      perl6: [
        "6pl",
        "6pm",
        "nqp",
        "p6",
        "p6l",
        "p6m",
        "pl",
        "pl6",
        "pm",
        "pm6",
        "t"
      ],
      piglatin: [
        "pig"
      ],
      pike: [
        "pike",
        "pmod"
      ],
      pod: [
        "pod"
      ],
      pogoscript: [
        "pogo"
      ],
      postscript: [
        "ps",
        "eps"
      ],
      powershell: [
        "ps1",
        "psd1",
        "psm1"
      ],
      processing: [
        "pde"
      ],
      prolog: [
        "pl",
        "ecl",
        "pro",
        "prolog"
      ],
      "propeller spin": [
        "spin"
      ],
      "protocol buffer": [
        "proto"
      ],
      "public key": [
        "asc",
        "pub"
      ],
      puppet: [
        "pp"
      ],
      "pure data": [
        "pd"
      ],
      purebasic: [
        "pb",
        "pbi"
      ],
      purescript: [
        "purs"
      ],
      python: [
        "py",
        "cgi",
        "fcgi",
        "gyp",
        "lmi",
        "pyde",
        "pyp",
        "pyt",
        "pyw",
        "tac",
        "wsgi",
        "xpy"
      ],
      "python traceback": [
        "pytb"
      ],
      qml: [
        "qml"
      ],
      qmake: [
        "pro",
        "pri"
      ],
      r: [
        "r",
        "rd",
        "rsx"
      ],
      raml: [
        "raml"
      ],
      rdoc: [
        "rdoc"
      ],
      realbasic: [
        "rbbas",
        "rbfrm",
        "rbmnu",
        "rbres",
        "rbtbar",
        "rbuistate"
      ],
      rhtml: [
        "rhtml"
      ],
      rmarkdown: [
        "rmd"
      ],
      racket: [
        "rkt",
        "rktd",
        "rktl",
        "scrbl"
      ],
      "ragel in ruby host": [
        "rl"
      ],
      "raw token data": [
        "raw"
      ],
      rebol: [
        "reb",
        "r",
        "r2",
        "r3",
        "rebol"
      ],
      red: [
        "red",
        "reds"
      ],
      redcode: [
        "cw"
      ],
      renderscript: [
        "rs",
        "rsh"
      ],
      robotframework: [
        "robot"
      ],
      rouge: [
        "rg"
      ],
      ruby: [
        "rb",
        "builder",
        "fcgi",
        "gemspec",
        "god",
        "irbrc",
        "jbuilder",
        "mspec",
        "pluginspec",
        "podspec",
        "rabl",
        "rake",
        "rbuild",
        "rbw",
        "rbx",
        "ru",
        "ruby",
        "thor",
        "watchr"
      ],
      rust: [
        "rs"
      ],
      sas: [
        "sas"
      ],
      scss: [
        "scss"
      ],
      sparql: [
        "sparql",
        "rq"
      ],
      sqf: [
        "sqf",
        "hqf"
      ],
      sql: [
        "sql",
        "cql",
        "ddl",
        "prc",
        "tab",
        "udf",
        "viw"
      ],
      sqlpl: [
        "sql",
        "db2"
      ],
      ston: [
        "ston"
      ],
      svg: [
        "svg"
      ],
      sage: [
        "sage",
        "sagews"
      ],
      saltstack: [
        "sls"
      ],
      sass: [
        "sass"
      ],
      scala: [
        "scala",
        "sbt",
        "sc"
      ],
      scaml: [
        "scaml"
      ],
      scheme: [
        "scm",
        "sld",
        "sls",
        "sps",
        "ss"
      ],
      scilab: [
        "sci",
        "sce",
        "tst"
      ],
      self: [
        "self"
      ],
      shell: [
        "sh",
        "bash",
        "bats",
        "cgi",
        "command",
        "fcgi",
        "ksh",
        "tmux",
        "tool",
        "zsh"
      ],
      shellsession: [
        "sh-session"
      ],
      shen: [
        "shen"
      ],
      slash: [
        "sl"
      ],
      slim: [
        "slim"
      ],
      smalltalk: [
        "st",
        "cs"
      ],
      smarty: [
        "tpl"
      ],
      sourcepawn: [
        "sp",
        "sma"
      ],
      squirrel: [
        "nut"
      ],
      "standard ml": [
        "ML",
        "fun",
        "sig",
        "sml"
      ],
      stata: [
        "do",
        "ado",
        "doh",
        "ihlp",
        "mata",
        "matah",
        "sthlp"
      ],
      stylus: [
        "styl"
      ],
      supercollider: [
        "scd",
        "sc"
      ],
      swift: [
        "swift"
      ],
      systemverilog: [
        "sv",
        "svh",
        "vh"
      ],
      toml: [
        "toml"
      ],
      txl: [
        "txl"
      ],
      tcl: [
        "tcl",
        "adp",
        "tm"
      ],
      tcsh: [
        "tcsh",
        "csh"
      ],
      tex: [
        "tex",
        "aux",
        "bbx",
        "bib",
        "cbx",
        "cls",
        "dtx",
        "ins",
        "lbx",
        "ltx",
        "mkii",
        "mkiv",
        "mkvi",
        "sty",
        "toc"
      ],
      tea: [
        "tea"
      ],
      text: [
        "txt",
        "fr"
      ],
      textile: [
        "textile"
      ],
      thrift: [
        "thrift"
      ],
      turing: [
        "t",
        "tu"
      ],
      turtle: [
        "ttl"
      ],
      twig: [
        "twig"
      ],
      typescript: [
        "ts"
      ],
      "unified parallel c": [
        "upc"
      ],
      unrealscript: [
        "uc"
      ],
      vcl: [
        "vcl"
      ],
      vhdl: [
        "vhdl",
        "vhd",
        "vhf",
        "vhi",
        "vho",
        "vhs",
        "vht",
        "vhw"
      ],
      vala: [
        "vala",
        "vapi"
      ],
      verilog: [
        "v",
        "veo"
      ],
      viml: [
        "vim"
      ],
      "visual basic": [
        "vb",
        "bas",
        "cls",
        "frm",
        "frx",
        "vba",
        "vbhtml",
        "vbs"
      ],
      volt: [
        "volt"
      ],
      "web ontology language": [
        "owl"
      ],
      webidl: [
        "webidl"
      ],
      xc: [
        "xc"
      ],
      xml: [
        "xml",
        "ant",
        "axml",
        "ccxml",
        "clixml",
        "cproject",
        "csproj",
        "ct",
        "dita",
        "ditamap",
        "ditaval",
        "dll.config",
        "filters",
        "fsproj",
        "fxml",
        "glade",
        "grxml",
        "ivy",
        "jelly",
        "kml",
        "launch",
        "mm",
        "mxml",
        "nproj",
        "nuspec",
        "odd",
        "osm",
        "plist",
        "pluginspec",
        "ps1xml",
        "psc1",
        "pt",
        "rdf",
        "rss",
        "scxml",
        "srdf",
        "storyboard",
        "stTheme",
        "sublime-snippet",
        "targets",
        "tmCommand",
        "tml",
        "tmLanguage",
        "tmPreferences",
        "tmSnippet",
        "tmTheme",
        "ts",
        "ui",
        "urdf",
        "vbproj",
        "vcxproj",
        "vxml",
        "wsdl",
        "wsf",
        "wxi",
        "wxl",
        "wxs",
        "x3d",
        "xacro",
        "xaml",
        "xib",
        "xlf",
        "xliff",
        "xmi",
        "xml.dist",
        "xsd",
        "xul",
        "zcml"
      ],
      xproc: [
        "xpl",
        "xproc"
      ],
      xquery: [
        "xquery",
        "xq",
        "xql",
        "xqm",
        "xqy"
      ],
      xs: [
        "xs"
      ],
      xslt: [
        "xslt",
        "xsl"
      ],
      xojo: [
        "xojo_code",
        "xojo_menu",
        "xojo_report",
        "xojo_script",
        "xojo_toolbar",
        "xojo_window"
      ],
      xtend: [
        "xtend"
      ],
      yaml: [
        "yml",
        "reek",
        "rviz",
        "yaml"
      ],
      zephir: [
        "zep"
      ],
      zimpl: [
        "zimpl",
        "zmpl",
        "zpl"
      ],
      desktop: [
        "desktop",
        "desktop.in"
      ],
      ec: [
        "ec",
        "eh"
      ],
      edn: [
        "edn"
      ],
      fish: [
        "fish"
      ],
      mupad: [
        "mu"
      ],
      nesc: [
        "nc"
      ],
      ooc: [
        "ooc"
      ],
      restructuredtext: [
        "rst",
        "rest"
      ],
      wisp: [
        "wisp"
      ],
      xbase: [
        "prg"
      ]
    };
  }
});

// ../../node_modules/lang-map/lib/lang.json
var require_lang = __commonJS({
  "../../node_modules/lang-map/lib/lang.json"(exports, module) {
    module.exports = {
      "1": [
        "groff"
      ],
      "2": [
        "groff"
      ],
      "3": [
        "groff"
      ],
      "4": [
        "groff"
      ],
      "5": [
        "groff"
      ],
      "6": [
        "groff"
      ],
      "7": [
        "groff"
      ],
      abap: [
        "abap"
      ],
      asc: [
        "ags script",
        "asciidoc",
        "public key"
      ],
      ash: [
        "ags script"
      ],
      ampl: [
        "ampl"
      ],
      g4: [
        "antlr"
      ],
      apl: [
        "apl"
      ],
      dyalog: [
        "apl"
      ],
      asp: [
        "asp"
      ],
      asax: [
        "asp"
      ],
      ascx: [
        "asp"
      ],
      ashx: [
        "asp"
      ],
      asmx: [
        "asp"
      ],
      aspx: [
        "asp"
      ],
      axd: [
        "asp"
      ],
      dats: [
        "ats"
      ],
      hats: [
        "ats"
      ],
      sats: [
        "ats"
      ],
      as: [
        "actionscript"
      ],
      adb: [
        "ada"
      ],
      ada: [
        "ada"
      ],
      ads: [
        "ada"
      ],
      agda: [
        "agda"
      ],
      als: [
        "alloy"
      ],
      apacheconf: [
        "apacheconf"
      ],
      cls: [
        "apex",
        "openedge abl",
        "tex",
        "visual basic"
      ],
      applescript: [
        "applescript"
      ],
      scpt: [
        "applescript"
      ],
      arc: [
        "arc"
      ],
      ino: [
        "arduino"
      ],
      asciidoc: [
        "asciidoc"
      ],
      adoc: [
        "asciidoc"
      ],
      aj: [
        "aspectj"
      ],
      asm: [
        "assembly"
      ],
      a51: [
        "assembly"
      ],
      nasm: [
        "assembly"
      ],
      aug: [
        "augeas"
      ],
      ahk: [
        "autohotkey"
      ],
      ahkl: [
        "autohotkey"
      ],
      au3: [
        "autoit"
      ],
      awk: [
        "awk"
      ],
      auk: [
        "awk"
      ],
      gawk: [
        "awk"
      ],
      mawk: [
        "awk"
      ],
      nawk: [
        "awk"
      ],
      bat: [
        "batchfile"
      ],
      cmd: [
        "batchfile"
      ],
      befunge: [
        "befunge"
      ],
      y: [
        "bison"
      ],
      bb: [
        "bitbake",
        "blitzbasic"
      ],
      decls: [
        "blitzbasic"
      ],
      bmx: [
        "blitzmax"
      ],
      bsv: [
        "bluespec"
      ],
      boo: [
        "boo"
      ],
      b: [
        "brainfuck",
        "limbo"
      ],
      bf: [
        "brainfuck"
      ],
      brs: [
        "brightscript"
      ],
      bro: [
        "bro"
      ],
      c: [
        "c"
      ],
      cats: [
        "c"
      ],
      h: [
        "c",
        "c++",
        "objective-c"
      ],
      idc: [
        "c"
      ],
      w: [
        "c"
      ],
      cs: [
        "c#",
        "smalltalk"
      ],
      cshtml: [
        "c#"
      ],
      csx: [
        "c#"
      ],
      cpp: [
        "c++"
      ],
      "c++": [
        "c++"
      ],
      cc: [
        "c++"
      ],
      cp: [
        "c++",
        "component pascal"
      ],
      cxx: [
        "c++"
      ],
      "h++": [
        "c++"
      ],
      hh: [
        "c++",
        "hack"
      ],
      hpp: [
        "c++"
      ],
      hxx: [
        "c++"
      ],
      inl: [
        "c++"
      ],
      ipp: [
        "c++"
      ],
      tcc: [
        "c++"
      ],
      tpp: [
        "c++"
      ],
      "c-objdump": [
        "c-objdump"
      ],
      chs: [
        "c2hs haskell"
      ],
      clp: [
        "clips"
      ],
      cmake: [
        "cmake"
      ],
      "cmake.in": [
        "cmake"
      ],
      cob: [
        "cobol"
      ],
      cbl: [
        "cobol"
      ],
      ccp: [
        "cobol"
      ],
      cobol: [
        "cobol"
      ],
      cpy: [
        "cobol"
      ],
      css: [
        "css"
      ],
      capnp: [
        "cap'n proto"
      ],
      mss: [
        "cartocss"
      ],
      ceylon: [
        "ceylon"
      ],
      chpl: [
        "chapel"
      ],
      ck: [
        "chuck"
      ],
      cirru: [
        "cirru"
      ],
      clw: [
        "clarion"
      ],
      icl: [
        "clean"
      ],
      dcl: [
        "clean"
      ],
      clj: [
        "clojure"
      ],
      boot: [
        "clojure"
      ],
      cl2: [
        "clojure"
      ],
      cljc: [
        "clojure"
      ],
      cljs: [
        "clojure"
      ],
      "cljs.hl": [
        "clojure"
      ],
      cljscm: [
        "clojure"
      ],
      cljx: [
        "clojure"
      ],
      hic: [
        "clojure"
      ],
      coffee: [
        "coffeescript"
      ],
      _coffee: [
        "coffeescript"
      ],
      cjsx: [
        "coffeescript"
      ],
      cson: [
        "coffeescript"
      ],
      iced: [
        "coffeescript"
      ],
      cfm: [
        "coldfusion"
      ],
      cfml: [
        "coldfusion"
      ],
      cfc: [
        "coldfusion cfc"
      ],
      lisp: [
        "common lisp",
        "newlisp"
      ],
      asd: [
        "common lisp"
      ],
      cl: [
        "common lisp",
        "cool",
        "opencl"
      ],
      lsp: [
        "common lisp",
        "newlisp"
      ],
      ny: [
        "common lisp"
      ],
      podsl: [
        "common lisp"
      ],
      cps: [
        "component pascal"
      ],
      coq: [
        "coq"
      ],
      v: [
        "coq",
        "verilog"
      ],
      cppobjdump: [
        "cpp-objdump"
      ],
      "c++-objdump": [
        "cpp-objdump"
      ],
      "c++objdump": [
        "cpp-objdump"
      ],
      "cpp-objdump": [
        "cpp-objdump"
      ],
      "cxx-objdump": [
        "cpp-objdump"
      ],
      creole: [
        "creole"
      ],
      cr: [
        "crystal"
      ],
      feature: [
        "cucumber"
      ],
      cu: [
        "cuda"
      ],
      cuh: [
        "cuda"
      ],
      cy: [
        "cycript"
      ],
      pyx: [
        "cython"
      ],
      pxd: [
        "cython"
      ],
      pxi: [
        "cython"
      ],
      d: [
        "d",
        "dtrace",
        "makefile"
      ],
      di: [
        "d"
      ],
      "d-objdump": [
        "d-objdump"
      ],
      com: [
        "digital command language"
      ],
      dm: [
        "dm"
      ],
      darcspatch: [
        "darcs patch"
      ],
      dpatch: [
        "darcs patch"
      ],
      dart: [
        "dart"
      ],
      diff: [
        "diff"
      ],
      patch: [
        "diff"
      ],
      dockerfile: [
        "dockerfile"
      ],
      djs: [
        "dogescript"
      ],
      dylan: [
        "dylan"
      ],
      dyl: [
        "dylan"
      ],
      intr: [
        "dylan"
      ],
      lid: [
        "dylan"
      ],
      E: [
        "e"
      ],
      ecl: [
        "ecl",
        "prolog"
      ],
      eclxml: [
        "ecl"
      ],
      sch: [
        "eagle",
        "kicad"
      ],
      brd: [
        "eagle"
      ],
      epj: [
        "ecere projects"
      ],
      e: [
        "eiffel"
      ],
      ex: [
        "elixir"
      ],
      exs: [
        "elixir"
      ],
      elm: [
        "elm"
      ],
      el: [
        "emacs lisp"
      ],
      emacs: [
        "emacs lisp"
      ],
      "emacs.desktop": [
        "emacs lisp"
      ],
      em: [
        "emberscript"
      ],
      emberscript: [
        "emberscript"
      ],
      erl: [
        "erlang"
      ],
      es: [
        "erlang"
      ],
      escript: [
        "erlang"
      ],
      hrl: [
        "erlang"
      ],
      fs: [
        "f#",
        "filterscript",
        "forth",
        "glsl"
      ],
      fsi: [
        "f#"
      ],
      fsx: [
        "f#"
      ],
      fx: [
        "flux"
      ],
      flux: [
        "flux"
      ],
      f90: [
        "fortran"
      ],
      f: [
        "fortran",
        "forth"
      ],
      f03: [
        "fortran"
      ],
      f08: [
        "fortran"
      ],
      f77: [
        "fortran"
      ],
      f95: [
        "fortran"
      ],
      for: [
        "fortran",
        "formatted",
        "forth"
      ],
      fpp: [
        "fortran"
      ],
      factor: [
        "factor"
      ],
      fy: [
        "fancy"
      ],
      fancypack: [
        "fancy"
      ],
      fan: [
        "fantom"
      ],
      fth: [
        "forth"
      ],
      "4th": [
        "forth"
      ],
      forth: [
        "forth"
      ],
      fr: [
        "forth",
        "frege",
        "text"
      ],
      frt: [
        "forth"
      ],
      g: [
        "g-code",
        "gap"
      ],
      gco: [
        "g-code"
      ],
      gcode: [
        "g-code"
      ],
      gms: [
        "gams"
      ],
      gap: [
        "gap"
      ],
      gd: [
        "gap",
        "gdscript"
      ],
      gi: [
        "gap"
      ],
      tst: [
        "gap",
        "scilab"
      ],
      s: [
        "gas"
      ],
      glsl: [
        "glsl"
      ],
      fp: [
        "glsl"
      ],
      frag: [
        "glsl",
        "javascript"
      ],
      frg: [
        "glsl"
      ],
      fshader: [
        "glsl"
      ],
      geo: [
        "glsl"
      ],
      geom: [
        "glsl"
      ],
      glslv: [
        "glsl"
      ],
      gshader: [
        "glsl"
      ],
      shader: [
        "glsl"
      ],
      vert: [
        "glsl"
      ],
      vrx: [
        "glsl"
      ],
      vshader: [
        "glsl"
      ],
      gml: [
        "game maker language",
        "graph modeling language"
      ],
      kid: [
        "genshi"
      ],
      ebuild: [
        "gentoo ebuild"
      ],
      eclass: [
        "gentoo eclass"
      ],
      po: [
        "gettext catalog"
      ],
      pot: [
        "gettext catalog"
      ],
      glf: [
        "glyph"
      ],
      gp: [
        "gnuplot"
      ],
      gnu: [
        "gnuplot"
      ],
      gnuplot: [
        "gnuplot"
      ],
      plot: [
        "gnuplot"
      ],
      plt: [
        "gnuplot"
      ],
      go: [
        "go"
      ],
      golo: [
        "golo"
      ],
      gs: [
        "gosu",
        "javascript"
      ],
      gst: [
        "gosu"
      ],
      gsx: [
        "gosu"
      ],
      vark: [
        "gosu"
      ],
      grace: [
        "grace"
      ],
      gradle: [
        "gradle"
      ],
      gf: [
        "grammatical framework"
      ],
      dot: [
        "graphviz (dot)"
      ],
      gv: [
        "graphviz (dot)"
      ],
      man: [
        "groff"
      ],
      groovy: [
        "groovy"
      ],
      grt: [
        "groovy"
      ],
      gtpl: [
        "groovy"
      ],
      gvy: [
        "groovy"
      ],
      gsp: [
        "groovy server pages"
      ],
      html: [
        "html"
      ],
      htm: [
        "html"
      ],
      "html.hl": [
        "html"
      ],
      st: [
        "html",
        "smalltalk"
      ],
      xht: [
        "html"
      ],
      xhtml: [
        "html"
      ],
      mustache: [
        "html+django"
      ],
      jinja: [
        "html+django"
      ],
      erb: [
        "html+erb"
      ],
      "erb.deface": [
        "html+erb"
      ],
      phtml: [
        "html+php"
      ],
      http: [
        "http"
      ],
      php: [
        "hack",
        "php"
      ],
      haml: [
        "haml"
      ],
      "haml.deface": [
        "haml"
      ],
      handlebars: [
        "handlebars"
      ],
      hbs: [
        "handlebars"
      ],
      hb: [
        "harbour"
      ],
      hs: [
        "haskell"
      ],
      hsc: [
        "haskell"
      ],
      hx: [
        "haxe"
      ],
      hxsl: [
        "haxe"
      ],
      hy: [
        "hy"
      ],
      pro: [
        "idl",
        "ini",
        "prolog",
        "qmake"
      ],
      dlm: [
        "idl"
      ],
      ipf: [
        "igor pro"
      ],
      ini: [
        "ini"
      ],
      cfg: [
        "ini"
      ],
      prefs: [
        "ini"
      ],
      properties: [
        "ini"
      ],
      irclog: [
        "irc log"
      ],
      weechatlog: [
        "irc log"
      ],
      idr: [
        "idris"
      ],
      lidr: [
        "idris"
      ],
      ni: [
        "inform 7"
      ],
      i7x: [
        "inform 7"
      ],
      iss: [
        "inno setup"
      ],
      io: [
        "io"
      ],
      ik: [
        "ioke"
      ],
      thy: [
        "isabelle"
      ],
      ijs: [
        "j"
      ],
      flex: [
        "jflex"
      ],
      jflex: [
        "jflex"
      ],
      json: [
        "json"
      ],
      lock: [
        "json"
      ],
      json5: [
        "json5"
      ],
      jsonld: [
        "jsonld"
      ],
      jq: [
        "jsoniq"
      ],
      jade: [
        "jade"
      ],
      j: [
        "jasmin",
        "objective-j"
      ],
      java: [
        "java"
      ],
      jsp: [
        "java server pages"
      ],
      js: [
        "javascript"
      ],
      _js: [
        "javascript"
      ],
      bones: [
        "javascript"
      ],
      es6: [
        "javascript"
      ],
      jake: [
        "javascript"
      ],
      jsb: [
        "javascript"
      ],
      jsfl: [
        "javascript"
      ],
      jsm: [
        "javascript"
      ],
      jss: [
        "javascript"
      ],
      jsx: [
        "javascript"
      ],
      njs: [
        "javascript"
      ],
      pac: [
        "javascript"
      ],
      sjs: [
        "javascript"
      ],
      ssjs: [
        "javascript"
      ],
      "sublime-build": [
        "javascript"
      ],
      "sublime-commands": [
        "javascript"
      ],
      "sublime-completions": [
        "javascript"
      ],
      "sublime-keymap": [
        "javascript"
      ],
      "sublime-macro": [
        "javascript"
      ],
      "sublime-menu": [
        "javascript"
      ],
      "sublime-mousemap": [
        "javascript"
      ],
      "sublime-project": [
        "javascript"
      ],
      "sublime-settings": [
        "javascript"
      ],
      "sublime-theme": [
        "javascript"
      ],
      "sublime-workspace": [
        "javascript"
      ],
      sublime_metrics: [
        "javascript"
      ],
      sublime_session: [
        "javascript"
      ],
      xsjs: [
        "javascript"
      ],
      xsjslib: [
        "javascript"
      ],
      jl: [
        "julia"
      ],
      krl: [
        "krl"
      ],
      kit: [
        "kit"
      ],
      kt: [
        "kotlin"
      ],
      ktm: [
        "kotlin"
      ],
      kts: [
        "kotlin"
      ],
      lfe: [
        "lfe"
      ],
      ll: [
        "llvm"
      ],
      lol: [
        "lolcode"
      ],
      lsl: [
        "lsl"
      ],
      lvproj: [
        "labview"
      ],
      lasso: [
        "lasso"
      ],
      las: [
        "lasso"
      ],
      lasso8: [
        "lasso"
      ],
      lasso9: [
        "lasso"
      ],
      ldml: [
        "lasso"
      ],
      latte: [
        "latte"
      ],
      lean: [
        "lean"
      ],
      hlean: [
        "lean"
      ],
      less: [
        "less"
      ],
      ly: [
        "lilypond"
      ],
      ily: [
        "lilypond"
      ],
      m: [
        "limbo",
        "m",
        "muf",
        "mathematica",
        "matlab",
        "mercury",
        "objective-c"
      ],
      ld: [
        "linker script"
      ],
      lds: [
        "linker script"
      ],
      liquid: [
        "liquid"
      ],
      lagda: [
        "literate agda"
      ],
      litcoffee: [
        "literate coffeescript"
      ],
      lhs: [
        "literate haskell"
      ],
      ls: [
        "livescript",
        "loomscript"
      ],
      _ls: [
        "livescript"
      ],
      xm: [
        "logos"
      ],
      x: [
        "logos"
      ],
      xi: [
        "logos"
      ],
      lgt: [
        "logtalk"
      ],
      logtalk: [
        "logtalk"
      ],
      lookml: [
        "lookml"
      ],
      lua: [
        "lua"
      ],
      fcgi: [
        "lua",
        "php",
        "perl",
        "python",
        "ruby",
        "shell"
      ],
      nse: [
        "lua"
      ],
      pd_lua: [
        "lua"
      ],
      rbxs: [
        "lua"
      ],
      wlua: [
        "lua"
      ],
      mumps: [
        "m"
      ],
      mtml: [
        "mtml"
      ],
      muf: [
        "muf"
      ],
      mak: [
        "makefile"
      ],
      mk: [
        "makefile"
      ],
      mako: [
        "mako"
      ],
      mao: [
        "mako"
      ],
      md: [
        "markdown"
      ],
      markdown: [
        "markdown"
      ],
      mkd: [
        "markdown"
      ],
      mkdn: [
        "markdown"
      ],
      mkdown: [
        "markdown"
      ],
      ron: [
        "markdown"
      ],
      mask: [
        "mask"
      ],
      mathematica: [
        "mathematica"
      ],
      cdf: [
        "mathematica"
      ],
      ma: [
        "mathematica"
      ],
      nb: [
        "mathematica"
      ],
      nbp: [
        "mathematica"
      ],
      wl: [
        "mathematica"
      ],
      wlt: [
        "mathematica"
      ],
      matlab: [
        "matlab"
      ],
      maxpat: [
        "max"
      ],
      maxhelp: [
        "max"
      ],
      maxproj: [
        "max"
      ],
      mxt: [
        "max"
      ],
      pat: [
        "max"
      ],
      mediawiki: [
        "mediawiki"
      ],
      moo: [
        "mercury",
        "moocode"
      ],
      minid: [
        "minid"
      ],
      druby: [
        "mirah"
      ],
      duby: [
        "mirah"
      ],
      mir: [
        "mirah"
      ],
      mirah: [
        "mirah"
      ],
      mo: [
        "modelica"
      ],
      mms: [
        "module management system"
      ],
      mmk: [
        "module management system"
      ],
      monkey: [
        "monkey"
      ],
      moon: [
        "moonscript"
      ],
      myt: [
        "myghty"
      ],
      nl: [
        "nl",
        "newlisp"
      ],
      nsi: [
        "nsis"
      ],
      nsh: [
        "nsis"
      ],
      n: [
        "nemerle"
      ],
      axs: [
        "netlinx"
      ],
      axi: [
        "netlinx"
      ],
      "axs.erb": [
        "netlinx+erb"
      ],
      "axi.erb": [
        "netlinx+erb"
      ],
      nlogo: [
        "netlogo"
      ],
      nginxconf: [
        "nginx"
      ],
      nim: [
        "nimrod"
      ],
      nimrod: [
        "nimrod"
      ],
      ninja: [
        "ninja"
      ],
      nit: [
        "nit"
      ],
      nix: [
        "nix"
      ],
      nu: [
        "nu"
      ],
      numpy: [
        "numpy"
      ],
      numpyw: [
        "numpy"
      ],
      numsc: [
        "numpy"
      ],
      ml: [
        "ocaml"
      ],
      eliom: [
        "ocaml"
      ],
      eliomi: [
        "ocaml"
      ],
      ml4: [
        "ocaml"
      ],
      mli: [
        "ocaml"
      ],
      mll: [
        "ocaml"
      ],
      mly: [
        "ocaml"
      ],
      objdump: [
        "objdump"
      ],
      mm: [
        "objective-c++",
        "xml"
      ],
      sj: [
        "objective-j"
      ],
      omgrofl: [
        "omgrofl"
      ],
      opa: [
        "opa"
      ],
      opal: [
        "opal"
      ],
      opencl: [
        "opencl"
      ],
      p: [
        "openedge abl"
      ],
      scad: [
        "openscad"
      ],
      org: [
        "org"
      ],
      ox: [
        "ox"
      ],
      oxh: [
        "ox"
      ],
      oxo: [
        "ox"
      ],
      oxygene: [
        "oxygene"
      ],
      oz: [
        "oz"
      ],
      pwn: [
        "pawn"
      ],
      aw: [
        "php"
      ],
      ctp: [
        "php"
      ],
      php3: [
        "php"
      ],
      php4: [
        "php"
      ],
      php5: [
        "php"
      ],
      phpt: [
        "php"
      ],
      pls: [
        "plsql"
      ],
      pkb: [
        "plsql"
      ],
      pks: [
        "plsql"
      ],
      plb: [
        "plsql"
      ],
      plsql: [
        "plsql"
      ],
      sql: [
        "plsql",
        "plpgsql",
        "sql",
        "sqlpl"
      ],
      pan: [
        "pan"
      ],
      psc: [
        "papyrus"
      ],
      parrot: [
        "parrot"
      ],
      pasm: [
        "parrot assembly"
      ],
      pir: [
        "parrot internal representation"
      ],
      pas: [
        "pascal"
      ],
      dfm: [
        "pascal"
      ],
      dpr: [
        "pascal"
      ],
      lpr: [
        "pascal"
      ],
      pp: [
        "pascal",
        "puppet"
      ],
      pl: [
        "perl",
        "perl6",
        "prolog"
      ],
      cgi: [
        "perl",
        "python",
        "shell"
      ],
      perl: [
        "perl"
      ],
      ph: [
        "perl"
      ],
      plx: [
        "perl"
      ],
      pm: [
        "perl",
        "perl6"
      ],
      pod: [
        "perl",
        "pod"
      ],
      psgi: [
        "perl"
      ],
      t: [
        "perl",
        "perl6",
        "turing"
      ],
      "6pl": [
        "perl6"
      ],
      "6pm": [
        "perl6"
      ],
      nqp: [
        "perl6"
      ],
      p6: [
        "perl6"
      ],
      p6l: [
        "perl6"
      ],
      p6m: [
        "perl6"
      ],
      pl6: [
        "perl6"
      ],
      pm6: [
        "perl6"
      ],
      pig: [
        "piglatin"
      ],
      pike: [
        "pike"
      ],
      pmod: [
        "pike"
      ],
      pogo: [
        "pogoscript"
      ],
      ps: [
        "postscript"
      ],
      eps: [
        "postscript"
      ],
      ps1: [
        "powershell"
      ],
      psd1: [
        "powershell"
      ],
      psm1: [
        "powershell"
      ],
      pde: [
        "processing"
      ],
      prolog: [
        "prolog"
      ],
      spin: [
        "propeller spin"
      ],
      proto: [
        "protocol buffer"
      ],
      pub: [
        "public key"
      ],
      pd: [
        "pure data"
      ],
      pb: [
        "purebasic"
      ],
      pbi: [
        "purebasic"
      ],
      purs: [
        "purescript"
      ],
      py: [
        "python"
      ],
      gyp: [
        "python"
      ],
      lmi: [
        "python"
      ],
      pyde: [
        "python"
      ],
      pyp: [
        "python"
      ],
      pyt: [
        "python"
      ],
      pyw: [
        "python"
      ],
      tac: [
        "python"
      ],
      wsgi: [
        "python"
      ],
      xpy: [
        "python"
      ],
      pytb: [
        "python traceback"
      ],
      qml: [
        "qml"
      ],
      pri: [
        "qmake"
      ],
      r: [
        "r",
        "rebol"
      ],
      rd: [
        "r"
      ],
      rsx: [
        "r"
      ],
      raml: [
        "raml"
      ],
      rdoc: [
        "rdoc"
      ],
      rbbas: [
        "realbasic"
      ],
      rbfrm: [
        "realbasic"
      ],
      rbmnu: [
        "realbasic"
      ],
      rbres: [
        "realbasic"
      ],
      rbtbar: [
        "realbasic"
      ],
      rbuistate: [
        "realbasic"
      ],
      rhtml: [
        "rhtml"
      ],
      rmd: [
        "rmarkdown"
      ],
      rkt: [
        "racket"
      ],
      rktd: [
        "racket"
      ],
      rktl: [
        "racket"
      ],
      scrbl: [
        "racket"
      ],
      rl: [
        "ragel in ruby host"
      ],
      raw: [
        "raw token data"
      ],
      reb: [
        "rebol"
      ],
      r2: [
        "rebol"
      ],
      r3: [
        "rebol"
      ],
      rebol: [
        "rebol"
      ],
      red: [
        "red"
      ],
      reds: [
        "red"
      ],
      cw: [
        "redcode"
      ],
      rs: [
        "renderscript",
        "rust"
      ],
      rsh: [
        "renderscript"
      ],
      robot: [
        "robotframework"
      ],
      rg: [
        "rouge"
      ],
      rb: [
        "ruby"
      ],
      builder: [
        "ruby"
      ],
      gemspec: [
        "ruby"
      ],
      god: [
        "ruby"
      ],
      irbrc: [
        "ruby"
      ],
      jbuilder: [
        "ruby"
      ],
      mspec: [
        "ruby"
      ],
      pluginspec: [
        "ruby",
        "xml"
      ],
      podspec: [
        "ruby"
      ],
      rabl: [
        "ruby"
      ],
      rake: [
        "ruby"
      ],
      rbuild: [
        "ruby"
      ],
      rbw: [
        "ruby"
      ],
      rbx: [
        "ruby"
      ],
      ru: [
        "ruby"
      ],
      ruby: [
        "ruby"
      ],
      thor: [
        "ruby"
      ],
      watchr: [
        "ruby"
      ],
      sas: [
        "sas"
      ],
      scss: [
        "scss"
      ],
      sparql: [
        "sparql"
      ],
      rq: [
        "sparql"
      ],
      sqf: [
        "sqf"
      ],
      hqf: [
        "sqf"
      ],
      cql: [
        "sql"
      ],
      ddl: [
        "sql"
      ],
      prc: [
        "sql"
      ],
      tab: [
        "sql"
      ],
      udf: [
        "sql"
      ],
      viw: [
        "sql"
      ],
      db2: [
        "sqlpl"
      ],
      ston: [
        "ston"
      ],
      svg: [
        "svg"
      ],
      sage: [
        "sage"
      ],
      sagews: [
        "sage"
      ],
      sls: [
        "saltstack",
        "scheme"
      ],
      sass: [
        "sass"
      ],
      scala: [
        "scala"
      ],
      sbt: [
        "scala"
      ],
      sc: [
        "scala",
        "supercollider"
      ],
      scaml: [
        "scaml"
      ],
      scm: [
        "scheme"
      ],
      sld: [
        "scheme"
      ],
      sps: [
        "scheme"
      ],
      ss: [
        "scheme"
      ],
      sci: [
        "scilab"
      ],
      sce: [
        "scilab"
      ],
      self: [
        "self"
      ],
      sh: [
        "shell"
      ],
      bash: [
        "shell"
      ],
      bats: [
        "shell"
      ],
      command: [
        "shell"
      ],
      ksh: [
        "shell"
      ],
      tmux: [
        "shell"
      ],
      tool: [
        "shell"
      ],
      zsh: [
        "shell"
      ],
      "sh-session": [
        "shellsession"
      ],
      shen: [
        "shen"
      ],
      sl: [
        "slash"
      ],
      slim: [
        "slim"
      ],
      tpl: [
        "smarty"
      ],
      sp: [
        "sourcepawn"
      ],
      sma: [
        "sourcepawn"
      ],
      nut: [
        "squirrel"
      ],
      ML: [
        "standard ml"
      ],
      fun: [
        "standard ml"
      ],
      sig: [
        "standard ml"
      ],
      sml: [
        "standard ml"
      ],
      do: [
        "stata"
      ],
      ado: [
        "stata"
      ],
      doh: [
        "stata"
      ],
      ihlp: [
        "stata"
      ],
      mata: [
        "stata"
      ],
      matah: [
        "stata"
      ],
      sthlp: [
        "stata"
      ],
      styl: [
        "stylus"
      ],
      scd: [
        "supercollider"
      ],
      swift: [
        "swift"
      ],
      sv: [
        "systemverilog"
      ],
      svh: [
        "systemverilog"
      ],
      vh: [
        "systemverilog"
      ],
      toml: [
        "toml"
      ],
      txl: [
        "txl"
      ],
      tcl: [
        "tcl"
      ],
      adp: [
        "tcl"
      ],
      tm: [
        "tcl"
      ],
      tcsh: [
        "tcsh"
      ],
      csh: [
        "tcsh"
      ],
      tex: [
        "tex"
      ],
      aux: [
        "tex"
      ],
      bbx: [
        "tex"
      ],
      bib: [
        "tex"
      ],
      cbx: [
        "tex"
      ],
      dtx: [
        "tex"
      ],
      ins: [
        "tex"
      ],
      lbx: [
        "tex"
      ],
      ltx: [
        "tex"
      ],
      mkii: [
        "tex"
      ],
      mkiv: [
        "tex"
      ],
      mkvi: [
        "tex"
      ],
      sty: [
        "tex"
      ],
      toc: [
        "tex"
      ],
      tea: [
        "tea"
      ],
      txt: [
        "text"
      ],
      textile: [
        "textile"
      ],
      thrift: [
        "thrift"
      ],
      tu: [
        "turing"
      ],
      ttl: [
        "turtle"
      ],
      twig: [
        "twig"
      ],
      ts: [
        "typescript",
        "xml"
      ],
      upc: [
        "unified parallel c"
      ],
      uc: [
        "unrealscript"
      ],
      vcl: [
        "vcl"
      ],
      vhdl: [
        "vhdl"
      ],
      vhd: [
        "vhdl"
      ],
      vhf: [
        "vhdl"
      ],
      vhi: [
        "vhdl"
      ],
      vho: [
        "vhdl"
      ],
      vhs: [
        "vhdl"
      ],
      vht: [
        "vhdl"
      ],
      vhw: [
        "vhdl"
      ],
      vala: [
        "vala"
      ],
      vapi: [
        "vala"
      ],
      veo: [
        "verilog"
      ],
      vim: [
        "viml"
      ],
      vb: [
        "visual basic"
      ],
      bas: [
        "visual basic"
      ],
      frm: [
        "visual basic"
      ],
      frx: [
        "visual basic"
      ],
      vba: [
        "visual basic"
      ],
      vbhtml: [
        "visual basic"
      ],
      vbs: [
        "visual basic"
      ],
      volt: [
        "volt"
      ],
      owl: [
        "web ontology language"
      ],
      webidl: [
        "webidl"
      ],
      xc: [
        "xc"
      ],
      xml: [
        "xml"
      ],
      ant: [
        "xml"
      ],
      axml: [
        "xml"
      ],
      ccxml: [
        "xml"
      ],
      clixml: [
        "xml"
      ],
      cproject: [
        "xml"
      ],
      csproj: [
        "xml"
      ],
      ct: [
        "xml"
      ],
      dita: [
        "xml"
      ],
      ditamap: [
        "xml"
      ],
      ditaval: [
        "xml"
      ],
      "dll.config": [
        "xml"
      ],
      filters: [
        "xml"
      ],
      fsproj: [
        "xml"
      ],
      fxml: [
        "xml"
      ],
      glade: [
        "xml"
      ],
      grxml: [
        "xml"
      ],
      ivy: [
        "xml"
      ],
      jelly: [
        "xml"
      ],
      kml: [
        "xml"
      ],
      launch: [
        "xml"
      ],
      mxml: [
        "xml"
      ],
      nproj: [
        "xml"
      ],
      nuspec: [
        "xml"
      ],
      odd: [
        "xml"
      ],
      osm: [
        "xml"
      ],
      plist: [
        "xml"
      ],
      ps1xml: [
        "xml"
      ],
      psc1: [
        "xml"
      ],
      pt: [
        "xml"
      ],
      rdf: [
        "xml"
      ],
      rss: [
        "xml"
      ],
      scxml: [
        "xml"
      ],
      srdf: [
        "xml"
      ],
      storyboard: [
        "xml"
      ],
      stTheme: [
        "xml"
      ],
      "sublime-snippet": [
        "xml"
      ],
      targets: [
        "xml"
      ],
      tmCommand: [
        "xml"
      ],
      tml: [
        "xml"
      ],
      tmLanguage: [
        "xml"
      ],
      tmPreferences: [
        "xml"
      ],
      tmSnippet: [
        "xml"
      ],
      tmTheme: [
        "xml"
      ],
      ui: [
        "xml"
      ],
      urdf: [
        "xml"
      ],
      vbproj: [
        "xml"
      ],
      vcxproj: [
        "xml"
      ],
      vxml: [
        "xml"
      ],
      wsdl: [
        "xml"
      ],
      wsf: [
        "xml"
      ],
      wxi: [
        "xml"
      ],
      wxl: [
        "xml"
      ],
      wxs: [
        "xml"
      ],
      x3d: [
        "xml"
      ],
      xacro: [
        "xml"
      ],
      xaml: [
        "xml"
      ],
      xib: [
        "xml"
      ],
      xlf: [
        "xml"
      ],
      xliff: [
        "xml"
      ],
      xmi: [
        "xml"
      ],
      "xml.dist": [
        "xml"
      ],
      xsd: [
        "xml"
      ],
      xul: [
        "xml"
      ],
      zcml: [
        "xml"
      ],
      xpl: [
        "xproc"
      ],
      xproc: [
        "xproc"
      ],
      xquery: [
        "xquery"
      ],
      xq: [
        "xquery"
      ],
      xql: [
        "xquery"
      ],
      xqm: [
        "xquery"
      ],
      xqy: [
        "xquery"
      ],
      xs: [
        "xs"
      ],
      xslt: [
        "xslt"
      ],
      xsl: [
        "xslt"
      ],
      xojo_code: [
        "xojo"
      ],
      xojo_menu: [
        "xojo"
      ],
      xojo_report: [
        "xojo"
      ],
      xojo_script: [
        "xojo"
      ],
      xojo_toolbar: [
        "xojo"
      ],
      xojo_window: [
        "xojo"
      ],
      xtend: [
        "xtend"
      ],
      yml: [
        "yaml"
      ],
      reek: [
        "yaml"
      ],
      rviz: [
        "yaml"
      ],
      yaml: [
        "yaml"
      ],
      zep: [
        "zephir"
      ],
      zimpl: [
        "zimpl"
      ],
      zmpl: [
        "zimpl"
      ],
      zpl: [
        "zimpl"
      ],
      desktop: [
        "desktop"
      ],
      "desktop.in": [
        "desktop"
      ],
      ec: [
        "ec"
      ],
      eh: [
        "ec"
      ],
      edn: [
        "edn"
      ],
      fish: [
        "fish"
      ],
      mu: [
        "mupad"
      ],
      nc: [
        "nesc"
      ],
      ooc: [
        "ooc"
      ],
      rst: [
        "restructuredtext"
      ],
      rest: [
        "restructuredtext"
      ],
      wisp: [
        "wisp"
      ],
      prg: [
        "xbase"
      ]
    };
  }
});

// ../../node_modules/lang-map/index.js
var require_lang_map = __commonJS({
  "../../node_modules/lang-map/index.js"(exports, module) {
    function map() {
      var cache = {};
      if (!cache.extensions) cache.extensions = require_exts();
      if (!cache.languages) cache.languages = require_lang();
      return cache;
    }
    map.extensions = function extensions(lang) {
      lang = normalize(lang);
      var langs = map().languages;
      var exts = map().extensions;
      return exts[lang] || exts[langs[lang]] || [lang];
    };
    map.languages = function languages(ext) {
      ext = normalize(ext);
      var langs = map().languages;
      var exts = map().extensions;
      return langs[ext] || langs[exts[ext]] || [ext];
    };
    function normalize(str) {
      if (str.charAt(0) === ".") {
        str = str.slice(1);
      }
      return str.toLowerCase();
    }
    module.exports = map;
  }
});
export default require_lang_map();
/*! Bundled license information:

lang-map/index.js:
  (*!
   * lang-map <https://github.com/jonschlinkert/lang-map>
   *
   * Copyright (c) 2014-2015, Jon Schlinkert.
   * Licensed under the MIT license.
   *)
*/
//# sourceMappingURL=lang-map.js.map
