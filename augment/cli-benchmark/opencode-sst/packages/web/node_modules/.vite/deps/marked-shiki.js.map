{"version": 3, "sources": ["../../../../../node_modules/marked-shiki/dist/index.js"], "sourcesContent": ["function o(s = {}) {\n  const { highlight: e, container: n } = s;\n  return {\n    async: !0,\n    async walkTokens(t) {\n      if (t.type !== \"code\" || typeof e != \"function\") return;\n      const [a = \"text\", ...i] = t.lang.split(\" \"), { text: c } = t, r = await e(c, a, i), l = n ? n.replace(\"%l\", String(a).toUpperCase()).replace(\"%s\", r).replace(\"%t\", c) : r;\n      Object.assign(t, {\n        type: \"html\",\n        block: !0,\n        text: `${l}\n`\n      });\n    }\n  };\n}\nexport {\n  o as default\n};\n"], "mappings": ";;;AAAA,SAAS,EAAE,IAAI,CAAC,GAAG;AACjB,QAAM,EAAE,WAAW,GAAG,WAAW,EAAE,IAAI;AACvC,SAAO;AAAA,IACL,OAAO;AAAA,IACP,MAAM,WAAW,GAAG;AAClB,UAAI,EAAE,SAAS,UAAU,OAAO,KAAK,WAAY;AACjD,YAAM,CAAC,IAAI,QAAQ,GAAG,CAAC,IAAI,EAAE,KAAK,MAAM,GAAG,GAAG,EAAE,MAAM,EAAE,IAAI,GAAG,IAAI,MAAM,EAAE,GAAG,GAAG,CAAC,GAAG,IAAI,IAAI,EAAE,QAAQ,MAAM,OAAO,CAAC,EAAE,YAAY,CAAC,EAAE,QAAQ,MAAM,CAAC,EAAE,QAAQ,MAAM,CAAC,IAAI;AAC1K,aAAO,OAAO,GAAG;AAAA,QACf,MAAM;AAAA,QACN,OAAO;AAAA,QACP,MAAM,GAAG,CAAC;AAAA;AAAA,MAEZ,CAAC;AAAA,IACH;AAAA,EACF;AACF;", "names": []}