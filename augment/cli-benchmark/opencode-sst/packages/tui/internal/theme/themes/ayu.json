{"$schema": "https://opencode.ai/theme.json", "defs": {"darkBg": "#0B0E14", "darkBgAlt": "#0D1017", "darkLine": "#11151C", "darkPanel": "#0F131A", "darkFg": "#BFBDB6", "darkFgMuted": "#565B66", "darkGutter": "#6C7380", "darkTag": "#39BAE6", "darkFunc": "#FFB454", "darkEntity": "#59C2FF", "darkString": "#AAD94C", "darkRegexp": "#95E6CB", "darkMarkup": "#F07178", "darkKeyword": "#FF8F40", "darkSpecial": "#E6B673", "darkComment": "#ACB6BF", "darkConstant": "#D2A6FF", "darkOperator": "#F29668", "darkAdded": "#7FD962", "darkRemoved": "#F26D78", "darkAccent": "#E6B450", "darkError": "#D95757", "darkIndentActive": "#6C7380"}, "theme": {"primary": "darkEntity", "secondary": "darkConstant", "accent": "darkAccent", "error": "darkError", "warning": "darkSpecial", "success": "darkAdded", "info": "darkTag", "text": "darkFg", "textMuted": "darkFgMuted", "background": "darkBg", "backgroundPanel": "darkPanel", "backgroundElement": "darkBgAlt", "border": "<PERSON><PERSON><PERSON>", "borderActive": "darkIndentActive", "borderSubtle": "darkLine", "diffAdded": "darkAdded", "diffRemoved": "darkRemoved", "diffContext": "darkComment", "diffHunkHeader": "darkComment", "diffHighlightAdded": "darkString", "diffHighlightRemoved": "darkMarkup", "diffAddedBg": "#20303b", "diffRemovedBg": "#37222c", "diffContextBg": "darkPanel", "diffLineNumber": "<PERSON><PERSON><PERSON>", "diffAddedLineNumberBg": "#1b2b34", "diffRemovedLineNumberBg": "#2d1f26", "markdownText": "darkFg", "markdownHeading": "darkConstant", "markdownLink": "darkEntity", "markdownLinkText": "darkTag", "markdownCode": "darkString", "markdownBlockQuote": "darkSpecial", "markdownEmph": "darkSpecial", "markdownStrong": "darkFunc", "markdownHorizontalRule": "darkFgMuted", "markdownListItem": "darkEntity", "markdownListEnumeration": "darkTag", "markdownImage": "darkEntity", "markdownImageText": "darkTag", "markdownCodeBlock": "darkFg", "syntaxComment": "darkComment", "syntaxKeyword": "darkKeyword", "syntaxFunction": "darkFunc", "syntaxVariable": "darkEntity", "syntaxString": "darkString", "syntaxNumber": "darkConstant", "syntaxType": "darkSpecial", "syntaxOperator": "darkOperator", "syntaxPunctuation": "darkFg"}}