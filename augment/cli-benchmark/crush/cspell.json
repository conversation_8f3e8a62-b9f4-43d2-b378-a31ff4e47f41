{"flagWords": [], "version": "0.2", "language": "en", "words": ["afero", "agentic", "alecthomas", "anthropics", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "azidentity", "bmatcuk", "bubbletea", "char<PERSON><PERSON><PERSON>", "charmbracelet", "charmtone", "<PERSON><PERSON><PERSON>", "chkconfig", "crush", "curlie", "cursorrules", "diffview", "doas", "<PERSON><PERSON><PERSON><PERSON>", "doublestar", "dpkg", "<PERSON><PERSON>", "fastwalk", "fdisk", "filepicker", "Focusable", "fseventsd", "fsext", "genai", "go<PERSON>y", "GROQ", "Guac", "imageorient", "Inex", "jetta", "jsons", "jsonschema", "jspm", "<PERSON><PERSON>", "killall", "<PERSON><PERSON><PERSON><PERSON>", "lipgloss", "LOCALAPPDATA", "lsps", "lucasb", "makepkg", "mcps", "MSYS", "mvdan", "natefinch", "nfnt", "noctx", "nohup", "nolint", "nslookup", "oksvg", "Oneshot", "openrouter", "opkg", "pacman", "paru", "pfctl", "postamble", "postambles", "preconfigured", "Preproc", "Proactiveness", "<PERSON><PERSON><PERSON><PERSON>", "pycache", "pytest", "qjebbs", "rasterx", "rivo", "sab<PERSON><PERSON>", "sess", "shortlog", "sj<PERSON>", "Sourcegraph", "sr<PERSON><PERSON>", "SSEMCP", "Streamable", "stretchr", "Strikethrough", "substrs", "Suscriber", "systeminfo", "tasklist", "termenv", "textinput", "tidwall", "timedout", "trashhalo", "udiff", "uniseg", "Unticked", "urllib", "USERPROFILE", "VERTEXAI", "webp", "whatis", "whereis", "sahilm", "csync"]}