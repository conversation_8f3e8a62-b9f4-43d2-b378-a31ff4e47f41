name: Update Schema

on:
  push:
    branches: [main]
    paths:
      - "internal/config/**"

jobs:
  update-schema:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          token: ${{ secrets.CRUSH_CLA_BOT }}
      - uses: actions/setup-go@v5
        with:
          go-version-file: go.mod
      - run: go run . schema > ./schema.json
      - uses: stefanzweifel/git-auto-commit-action@778341af668090896ca464160c2def5d1d1a3eb0 # v5
        with:
          commit_message: "chore: auto-update generated files"
          branch: main
          commit_user_name: actions-user
          commit_user_email: <EMAIL>
          commit_author: actions-user <<EMAIL>>
