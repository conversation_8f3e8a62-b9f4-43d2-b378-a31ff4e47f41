name: nightly

on:
  schedule:
    - cron: "0 0 * * *" # every day at midnight
  workflow_dispatch: # allows manual triggering

jobs:
  check:
    runs-on: ubuntu-latest
    outputs:
      should_run: ${{ steps.check.outputs.should_run }}
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 1
      - id: check
        env:
          GITHUB_TOKEN: "${{ secrets.GITHUB_TOKEN }}"
        run: |
          if gh run list --workflow nightly.yml -c $(git rev-parse HEAD) -s success | grep 'completed'; then
            echo "should_run=false" >> $GITHUB_OUTPUT
          else
            echo "should_run=true" >> $GITHUB_OUTPUT
          fi
  nightly:
    needs: check
    if: needs.check.outputs.should_run == 'true'
    uses: charmbracelet/meta/.github/workflows/nightly.yml@main
    secrets:
      goreleaser_key: ${{ secrets.GORELEASER_KEY }}
