# yaml-language-server: $schema=https://json.schemastore.org/github-workflow.json

name: goreleaser

on:
  push:
    tags:
      - v*.*.*

concurrency:
  group: goreleaser
  cancel-in-progress: true

jobs:
  goreleaser:
    uses: charmbracelet/meta/.github/workflows/goreleaser.yml@main
    with:
      macos_sign_entitlements: "./.github/entitlements.plist"
    secrets:
      docker_username: ${{ secrets.DOCKERHUB_USERNAME }}
      docker_token: ${{ secrets.DOCKERHUB_TOKEN }}
      gh_pat: ${{ secrets.PERSONAL_ACCESS_TOKEN }}
      goreleaser_key: ${{ secrets.GORELEASER_KEY }}
      fury_token: ${{ secrets.FURY_TOKEN }}
      nfpm_gpg_key: ${{ secrets.NFPM_GPG_KEY }}
      nfpm_passphrase: ${{ secrets.NFPM_PASSPHRASE }}
      npm_token: ${{ secrets.NPM_TOKEN }}
      snapcraft_token: ${{ secrets.SNAPCRAFT_TOKEN }}
      aur_key: ${{ secrets.AUR_KEY }}
      macos_sign_p12: ${{ secrets.MACOS_SIGN_P12 }}
      macos_sign_password: ${{ secrets.MACOS_SIGN_PASSWORD }}
      macos_notary_issuer_id: ${{ secrets.MACOS_NOTARY_ISSUER_ID }}
      macos_notary_key_id: ${{ secrets.MACOS_NOTARY_KEY_ID }}
      macos_notary_key: ${{ secrets.MACOS_NOTARY_KEY }}
