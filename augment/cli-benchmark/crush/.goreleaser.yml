# yaml-language-server: $schema=https://goreleaser.com/static/schema-pro.json
# vim: set ts=2 sw=2 tw=0 fo=jcroql
version: 2

project_name: crush

includes:
  - from_url:
      url: charmbracelet/meta/main/notarize.yaml

metadata:
  mod_timestamp: "{{ .CommitTimestamp }}"
  license: "FSL-1.1-MIT"
  homepage: "https://charm.sh/crush"
  description: "A powerful terminal-based AI assistant for developers, providing intelligent coding assistance directly in your terminal."
  maintainers:
    - "kujtimiihoxha <<EMAIL>>"
    - "caarlos0 <<EMAIL>>" # for aur

nightly:
  publish_release: true
  keep_single_release: true
  version_template: "{{ incminor .Version }}-nightly"

snapshot:
  version_template: "0.0.0-{{ .Timestamp }}"

before:
  hooks:
    - go mod tidy
    - rm -rf completions
    - mkdir completions
    - rm -rf manpages
    - mkdir manpages
    - sh -c 'go run . completion bash >./completions/crush.bash'
    - sh -c 'go run . completion zsh >./completions/crush.zsh'
    - sh -c 'go run . completion fish >./completions/crush.fish'
    - sh -c 'go run . man | gzip -c >./manpages/crush.1.gz'

# gomod:
#   proxy: true

builds:
  - env:
      - CGO_ENABLED=0
    goos:
      - linux
      - darwin
      - windows
      - freebsd
      - openbsd
      - netbsd
    goarch:
      - amd64
      - arm64
      - "386"
      - arm
    goarm:
      - "7"

    ldflags:
      - -s -w -X github.com/charmbracelet/crush/internal/version.Version={{.Version}}

archives:
  - name_template: >-
      crush_
      {{- .Version }}_
      {{- title .Os }}_
      {{- if eq .Arch "amd64" }}x86_64
      {{- else if eq .Arch "386" }}i386
      {{- else }}{{ .Arch }}{{ end }}
      {{- with .Arm}}v{{ . }}{{ end }}
    wrap_in_directory: true
    files:
      - README*
      - LICENSE*
      - manpages/*
      - completions/*
    format_overrides:
      - goos: windows
        formats: [zip]

checksum:
  name_template: "checksums.txt"

aur_sources:
  - private_key: "{{ .Env.AUR_KEY }}"
    git_url: "ssh://<EMAIL>/crush.git"
    makedepends:
      - go
      - git
    prepare: |-
      go mod download
    build: |-
      mkdir completions
      mkdir manpages
      export CGO_CPPFLAGS="${CPPFLAGS}"
      export CGO_CFLAGS="${CFLAGS}"
      export CGO_CXXFLAGS="${CXXFLAGS}"
      export CGO_LDFLAGS="${LDFLAGS}"
      export GOFLAGS="-buildmode=pie -trimpath -mod=readonly -modcacherw"
      go build -ldflags="-w -s -buildid='' -linkmode=external -X main.version=v${pkgver}" .
      ./crush completion bash >./completions/crush.bash
      ./crush completion zsh >./completions/crush.zsh
      ./crush completion fish >./completions/crush.fish
      ./crush man | gzip -c >./manpages/crush.1.gz
    package: |-
      # bin
      install -Dm755 "./crush" "${pkgdir}/usr/bin/crush"
      # license
      mkdir -p "${pkgdir}/usr/share/licenses/crush/"
      install -Dm644 ./LICENSE* "${pkgdir}/usr/share/licenses/crush/"
      # completions
      mkdir -p "${pkgdir}/usr/share/bash-completion/completions/"
      mkdir -p "${pkgdir}/usr/share/zsh/site-functions/"
      mkdir -p "${pkgdir}/usr/share/fish/vendor_completions.d/"
      install -Dm644 "./completions/crush.bash" "${pkgdir}/usr/share/bash-completion/completions/crush"
      install -Dm644 "./completions/crush.zsh" "${pkgdir}/usr/share/zsh/site-functions/_crush"
      install -Dm644 "./completions/crush.fish" "${pkgdir}/usr/share/fish/vendor_completions.d/crush.fish"
      # man pages
      install -Dm644 "./manpages/crush.1.gz" "${pkgdir}/usr/share/man/man1/crush.1.gz"
      # readme
      mkdir -pv "${pkgdir}/usr/share/doc/crush/"
      install -Dm644 README* "${pkgdir}/usr/share/doc/crush/"

aurs:
  - private_key: "{{ .Env.AUR_KEY }}"
    git_url: "ssh://<EMAIL>/crush-bin.git"
    provides:
      - crush
    conflicts:
      - crush
    package: |-
      cd "${srcdir}/crush_${pkgver}_Linux_${CARCH}"
      # bin
      install -Dm755 "./crush" "${pkgdir}/usr/bin/crush"
      # license
      mkdir -p "${pkgdir}/usr/share/licenses/crush/"
      install -Dm644 ./LICENSE* "${pkgdir}/usr/share/licenses/crush/"
      # completions
      mkdir -p "${pkgdir}/usr/share/bash-completion/completions/"
      mkdir -p "${pkgdir}/usr/share/zsh/site-functions/"
      mkdir -p "${pkgdir}/usr/share/fish/vendor_completions.d/"
      install -Dm644 "./completions/crush.bash" "${pkgdir}/usr/share/bash-completion/completions/crush"
      install -Dm644 "./completions/crush.zsh" "${pkgdir}/usr/share/zsh/site-functions/_crush"
      install -Dm644 "./completions/crush.fish" "${pkgdir}/usr/share/fish/vendor_completions.d/crush.fish"
      # man pages
      install -Dm644 "./manpages/crush.1.gz" "${pkgdir}/usr/share/man/man1/crush.1.gz"
      # readme
      mkdir -pv "${pkgdir}/usr/share/doc/crush/"
      install -Dm644 README* "${pkgdir}/usr/share/doc/crush/"

furies:
  - disable: "{{ .IsNightly }}"
    account: "{{ with .Env.FURY_TOKEN }}charmcli{{ else }}{{ end }}"
    secret_name: FURY_TOKEN

brews:
  - repository:
      owner: charmbracelet
      name: homebrew-tap
      token: "{{ .Env.HOMEBREW_TAP_GITHUB_TOKEN }}"
    goarm: 7
    extra_install: |-
      bash_completion.install "completions/{{ .ProjectName }}.bash" => "{{ .ProjectName }}"
      zsh_completion.install "completions/{{ .ProjectName }}.zsh" => "_{{ .ProjectName }}"
      fish_completion.install "completions/{{ .ProjectName }}.fish"
      man1.install "manpages/{{ .ProjectName }}.1.gz"

npms:
  - name: "@charmland/crush"
    repository: "git+https://github.com/charmbracelet/crush.git"
    bugs: https://github.com/charmbracelet/crush/issues
    access: public

nfpms:
  - formats:
      - apk
      - deb
      - rpm
      - archlinux
    file_name_template: "{{ .ConventionalFileName }}"
    contents:
      - src: ./completions/crush.bash
        dst: /etc/bash_completion.d/crush
      - src: ./completions/crush.fish
        dst: /usr/share/fish/vendor_completions.d/crush.fish
      - src: ./completions/crush.zsh
        dst: /usr/share/zsh/site-functions/_crush
      - src: ./manpages/crush.1.gz
        dst: /usr/share/man/man1/crush.1.gz
    rpm:
      signature:
        key_file: '{{ if ne (index .Env "GPG_KEY_PATH") "" }}{{ .Env.GPG_KEY_PATH }}{{ else }}{{ end }}'
    deb:
      signature:
        key_file: '{{ if ne (index .Env "GPG_KEY_PATH") "" }}{{ .Env.GPG_KEY_PATH }}{{ else }}{{ end }}'

signs:
  - cmd: cosign
    certificate: "${artifact}.pem"
    args:
      - sign-blob
      - "--output-certificate=${certificate}"
      - "--output-signature=${signature}"
      - "${artifact}"
      - "--yes"
    artifacts: checksum
    output: true

source:
  enabled: true

sboms:
  - artifacts: archive
  - id: source
    artifacts: source

nix:
  - repository:
      owner: "charmbracelet"
      name: nur
      token: "{{ .Env.HOMEBREW_TAP_GITHUB_TOKEN }}"
    license: fsl11Mit
    extra_install: |-
      installManPage ./manpages/crush.1.gz
      installShellCompletion ./completions/*

winget:
  - publisher: charmbracelet
    copyright: Charmbracelet, Inc
    repository:
      owner: "charmbracelet"
      name: winget-pkgs
      token: "{{ .Env.HOMEBREW_TAP_GITHUB_TOKEN }}"
      branch: "crush-{{.Version}}"
      pull_request:
        enabled: true
        draft: false
        check_boxes: true
        base:
          owner: microsoft
          name: winget-pkgs
          branch: master

changelog:
  sort: asc
  disable: "{{ .IsNightly }}"
  use: github
  filters:
    exclude:
      - "^(build|ci): "
      - "^chore: auto-update generated files$"
      - "^chore: docs$"
      - "^chore: schema update$"
      - "^chore: schema$"
      - "^chore: typo$"
      - "^chore: update schema$"
      - "^chore: update$"
      - "^chore\\(deps\\): "
      - "^docs: update$"
      - "^test:"
      - "^test\\("
      - "merge conflict"
      - "merge conflict"
      - Merge branch
      - Merge pull request
      - Merge remote-tracking branch
      - go mod tidy
      - "^wip "
      - "^wip:"
  groups:
    - title: "New Features"
      regexp: '^.*?feat(\(.+\))??!?:.+$'
      order: 100
    - title: "Security updates"
      regexp: '^.*?sec(\(.+\))??!?:.+$'
      order: 150
    - title: "Bug fixes and improvements"
      regexp: '^.*?(fix|refactor)(\(.+\))??!?:.+$'
      order: 200
    - title: "Documentation updates"
      regexp: ^.*?docs?(\(.+\))??!?:.+$
      order: 400
    - title: Other work
      order: 9999

release:
  prerelease: auto
  footer:
    from_url:
      url: https://raw.githubusercontent.com/charmbracelet/meta/main/footer.md
