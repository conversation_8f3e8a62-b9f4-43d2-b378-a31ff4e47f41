package splash

import (
	"github.com/charmbracelet/bubbles/v2/key"
)

type KeyMap struct {
	Select,
	Next,
	Previous,
	Yes,
	No,
	Tab,
	LeftRight,
	Back key.Binding
}

func DefaultKeyMap() KeyMap {
	return KeyMap{
		Select: key.NewBinding(
			key.WithKeys("enter", "ctrl+y"),
			key.WithHelp("enter", "confirm"),
		),
		Next: key.NewBinding(
			key.WithKeys("down", "ctrl+n"),
			key.WithHelp("↓", "next item"),
		),
		Previous: key.NewBinding(
			key.WithKeys("up", "ctrl+p"),
			key.With<PERSON>elp("↑", "previous item"),
		),
		Yes: key.NewBinding(
			key.With<PERSON><PERSON><PERSON>("y", "Y"),
			key.WithHelp("y", "yes"),
		),
		No: key.NewBinding(
			key.WithKeys("n", "N"),
			key.WithHelp("n", "no"),
		),
		Tab: key.NewBinding(
			key.<PERSON><PERSON><PERSON><PERSON>("tab"),
			key.With<PERSON>elp("tab", "switch"),
		),
		LeftRight: key.NewBinding(
			key.With<PERSON><PERSON><PERSON>("left", "right"),
			key.WithHelp("←/→", "switch"),
		),
		Back: key.NewBinding(
			key.WithKeys("esc"),
			key.WithHelp("esc", "back"),
		),
	}
}
