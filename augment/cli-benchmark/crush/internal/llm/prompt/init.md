`Please analyze this codebase and create a **CRUSH.md** file containing:

- Build/lint/test commands - especially for running a single test
- Code style guidelines including imports, formatting, types, naming conventions, error handling, etc.

The file you create will be given to agentic coding agents (such as yourself) that operate in this repository. Make it about 20-30 lines long.
If there's already a **CRUSH.md**, improve it.

If there are Cursor rules (in `.cursor/rules/` or `.cursorrules`) or Copilot rules (in `.github/copilot-instructions.md`), make sure to include them.
Add the `.crush` directory to the `.gitignore` file if it's not already there.
