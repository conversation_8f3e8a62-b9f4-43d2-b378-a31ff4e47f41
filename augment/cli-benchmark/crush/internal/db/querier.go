// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0

package db

import (
	"context"
)

type Querier interface {
	CreateFile(ctx context.Context, arg CreateFileParams) (File, error)
	CreateMessage(ctx context.Context, arg CreateMessageParams) (Message, error)
	CreateSession(ctx context.Context, arg CreateSessionParams) (Session, error)
	DeleteFile(ctx context.Context, id string) error
	DeleteMessage(ctx context.Context, id string) error
	DeleteSession(ctx context.Context, id string) error
	DeleteSessionFiles(ctx context.Context, sessionID string) error
	DeleteSessionMessages(ctx context.Context, sessionID string) error
	GetFile(ctx context.Context, id string) (File, error)
	GetFileByPathAndSession(ctx context.Context, arg GetFileByPathAndSessionParams) (File, error)
	GetMessage(ctx context.Context, id string) (Message, error)
	GetSessionByID(ctx context.Context, id string) (Session, error)
	ListFilesByPath(ctx context.Context, path string) ([]File, error)
	ListFilesBySession(ctx context.Context, sessionID string) ([]File, error)
	ListLatestSessionFiles(ctx context.Context, sessionID string) ([]File, error)
	ListMessagesBySession(ctx context.Context, sessionID string) ([]Message, error)
	ListNewFiles(ctx context.Context) ([]File, error)
	ListSessions(ctx context.Context) ([]Session, error)
	UpdateMessage(ctx context.Context, arg UpdateMessageParams) error
	UpdateSession(ctx context.Context, arg UpdateSessionParams) (Session, error)
}

var _ Querier = (*Queries)(nil)
